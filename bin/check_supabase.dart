import 'dart:io';
import 'package:supabase/supabase.dart';

/// Simple CLI to verify Supabase connection and anonymous auth.
/// Run with: dart run bin/check_supabase.dart
Future<void> main() async {
  final env = await _readEnv('.env');
  final url = env['SUPABASE_URL'] ?? const String.fromEnvironment('SUPABASE_URL');
  final anonKey = env['SUPABASE_ANON_KEY'] ?? const String.fromEnvironment('SUPABASE_ANON_KEY');

  if ((url == null || url.isEmpty) || (anonKey == null || anonKey.isEmpty)) {
    stderr.writeln('Missing SUPABASE_URL or SUPABASE_ANON_KEY.');
    exitCode = 1;
    return;
  }

  final client = SupabaseClient(url, anonKey);

  // Anonymous auth test
  try {
    await client.auth.signInAnonymously();
    final current = (await client.auth.getUser()).user;
    stdout.writeln('Auth user: ${current?.id}');
  } catch (e) {
    stderr.writeln('Auth failed: $e');
  }

  // Basic RPC read test (safe): get_country_stats
  try {
    final res = await client.rpc('get_country_stats');
    if (res.data is List) {
      stdout.writeln('get_country_stats rows: ${(res.data as List).length}');
    } else {
      stdout.writeln('get_country_stats response: ${res.data}');
    }
  } catch (e) {
    stderr.writeln('RPC get_country_stats failed: $e');
  }

  // Basic insert test: insert a harmless score (only if run with TEST_MODE=1)
  final testMode = (Platform.environment['TEST_MODE'] == '1');
  if (testMode) {
    try {
      final uid = (await client.auth.getUser()).user!.id;
      await client.from('scores').insert({
        'user_id': uid,
        'ms': 9999, // near-upper bound test
      });
      stdout.writeln('Insert test score success.');
    } catch (e) {
      stderr.writeln('Insert failed: $e');
    }
  }
}

Future<Map<String, String>> _readEnv(String path) async {
  final file = File(path);
  if (!await file.exists()) return {};
  final lines = await file.readAsLines();
  final Map<String, String> env = {};
  for (final raw in lines) {
    final line = raw.trim();
    if (line.isEmpty || line.startsWith('#')) continue;
    final idx = line.indexOf('=');
    if (idx <= 0) continue;
    final key = line.substring(0, idx).trim();
    final value = line.substring(idx + 1).trim();
    env[key] = value;
  }
  return env;
}

