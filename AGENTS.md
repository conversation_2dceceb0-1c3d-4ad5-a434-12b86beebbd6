# Repository Guidelines

## Project Structure & Module Organization
- lib/: App code. Key areas: `screens/` (UI), `widgets/` (reusable UI), `controller/` (GetX), `services/` (e.g., `supabase_service.dart`), `routes/` (navigation), `util/`, `l10n/` (ARB + generated localizations), `main.dart`.
- assets/: Icons, tier badges, sounds. Declared in `pubspec.yaml` (also loads `.env`).
- bin/: Utilities (e.g., `bin/check_supabase.dart`).
- test/: Widget/unit tests (e.g., `widget_test.dart`).
- Platforms: `android/`, `ios/`, `web/`. Config: `analysis_options.yaml`, `pubspec.yaml`.

## Build, Test, and Development Commands
- `flutter pub get`: Install dependencies.
- `flutter run`: Run the app (use `-d chrome` for web or pick a device).
- `flutter test`: Run tests.
- `flutter analyze`: Static analysis using flutter_lints.
- `dart format .`: Auto-format Dart code.
- `dart run bin/check_supabase.dart`: Verify Supabase connectivity (requires `.env`).
- `flutter build apk --release` | `ios` | `web`: Create release builds.

## Coding Style & Naming Conventions
- **Indentation**: 2 spaces; prefer small, focused widgets and controllers.
- **Lints**: Uses `flutter_lints` (see `analysis_options.yaml`). Fix analyzer warnings before PRs.
- **Naming**: New files use lower_snake_case (e.g., `speed_test_screen.dart`). Classes: UpperCamelCase. Methods/vars: lowerCamelCase. Keep existing file names when editing to avoid churn.
- **Imports**: Prefer relative within `lib/`; avoid unused imports.

## Testing Guidelines
- **Framework**: `flutter_test` with widget tests. Place tests in `test/` mirroring `lib/` and suffix with `_test.dart`.
- **Run**: `flutter test` (optionally `--coverage`).
- **Focus**: Cover business logic in `services/` and critical flows in `screens/` with widget tests.

## Commit & Pull Request Guidelines
- **Commits**: Imperative, concise. Conventional Commits encouraged: `feat:`, `fix:`, `refactor:`, `test:`, `docs:`, `chore:` (e.g., `feat(speed): add go-sound feedback`).
- **PRs**: Clear description, linked issues, screenshots/GIFs for UI changes, test updates if logic changes, pass `flutter analyze` and `dart format`.

## Security & Configuration Tips
- **Environment**: Create `.env` with `SUPABASE_URL=` and `SUPABASE_ANON_KEY=` (do not commit secrets). The app loads it at startup.
- **Connectivity**: Verify backend access with `dart run bin/check_supabase.dart` before testing auth/leaderboards.
- **Localization**: Edit ARB files in `lib/l10n/`; codegen runs during build (`flutter gen-l10n` if needed).


