I have localization files in the `/lib/l10n` directory that contain translated text for different languages. I need you to:

1. First, examine the localization files in `/lib/l10n` to understand the available translation keys and supported languages
2. Identify all hardcoded text strings in the Flutter app that should be replaced with localized versions
3. Replace the hardcoded strings with proper localization calls using Flutter's internationalization (i18n) framework
4. Ensure that the app properly uses the localized strings instead of hardcoded text

Please start by examining the localization files to understand the structure and available translations, then systematically find and replace hardcoded strings throughout the codebase with their localized equivalents.