import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_reaction_speed/main.dart';
import 'package:flutter/services.dart';

/// 글래스모달의 내용물을 "가운데 정렬"로 배치한 버전
/// - 중앙 정렬(가로/세로) + 가운데 텍스트 정렬
/// - 닫기 버튼은 우측 상단 고정, 본문은 센터에 모여 보이도록 minHeight 제공
/// - 기존 함수명과 충돌을 피하려고 별도 이름을 사용했습니다.
Future<Future<Object?>> showHowToPlayGlassModalCentered(
    BuildContext context, {
      VoidCallback? onStart,
    }) async {
  HapticFeedback.lightImpact();
  return showGeneralDialog(
    context: context,
    barrierDismissible: true,
    barrierLabel: context.l10n.how_title,
    barrierColor: Colors.black.withOpacity(0.35),
    transitionDuration: const Duration(milliseconds: 260),
    pageBuilder: (_, __, ___) => const SizedBox.shrink(),
    transitionBuilder: (context, animation, secondary, child) {
      final curved = CurvedAnimation(parent: animation, curve: Curves.easeOutBack, reverseCurve: Curves.easeInCubic);
      return Opacity(
        opacity: animation.value,
        child: Transform.scale(
          scale: Tween<double>(begin: 0.94, end: 1).evaluate(curved),
          child: _GlassDialogCentered(onStart: onStart),
        ),
      );
    },
  );
}

class _GlassDialogCentered extends StatelessWidget {
  const _GlassDialogCentered({this.onStart});
  final VoidCallback? onStart;

  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;
    final size = MediaQuery.of(context).size;

    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 24),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: 520,
            // 높이를 어느 정도 확보해서 내용이 "가운데"에 위치하도록 유도
            minHeight: size.height * 0.46,
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(24),
            child: Stack(
              children: [
                // 유리 질감 배경
                BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 18, sigmaY: 18),
                  child: Container(),
                ),
                // 반투명 그라디언트 + 라이트 보더/글로우
                DecoratedBox(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        cs.surfaceVariant.withOpacity(0.65),
                        cs.primaryContainer.withOpacity(0.35),
                      ],
                    ),
                    border: Border.all(color: Colors.white.withOpacity(0.18)),
                    boxShadow: [
                      BoxShadow(
                        color: cs.primary.withOpacity(0.18),
                        blurRadius: 30,
                        offset: const Offset(0, 18),
                      ),
                    ],
                  ),
                ),

                // 닫기 버튼 (우상단 고정)
                Positioned(
                  top: 4,
                  right: 4,
                  child: IconButton(
                    visualDensity: VisualDensity.compact,
                    onPressed: () => Navigator.of(context).maybePop(),
                    icon: const Icon(Icons.close_rounded),
                    tooltip: context.l10n.common_close,
                  ),
                ),

                // 본문을 중앙 정렬로 배치
                Align(
                  alignment: Alignment.center,
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(20, 24, 20, 20),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // 타이틀
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.auto_awesome, color: cs.primary),
                            const SizedBox(width: 8),
                            Text(
                              context.l10n.how_title,
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w800),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                        const SizedBox(height: 10),
                        Text(
                          context.l10n.how_step_intro,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: cs.onSurfaceVariant),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 18),
                        // 스텝들을 위아래로 배치하고 가운데 정렬
                        _StepCenter(
                          icon: Icons.circle,
                          iconColor: Colors.green,
                          title: context.l10n.test_hint_waiting,
                          subtitle: context.l10n.test_too_soon_hint,
                        ),
                        const SizedBox(height: 12),
                        _StepCenter(
                          icon: Icons.timer_outlined,
                          title: context.l10n.how_step_trials,
                          subtitle: context.l10n.leader_my_best_ms('{ms}').replaceAll(RegExp(r'\{ms\}.*'), ''),
                        ),
                        const SizedBox(height: 12),
                        _StepCenter(
                          icon: Icons.public,
                          title: context.l10n.leader_country_avg_ms('{ms}').replaceAll(RegExp(r'\{ms\}.*'), ''),
                          subtitle: context.l10n.how_step_board,
                        ),

                        const SizedBox(height: 22),
                        // CTA
                        SizedBox(
                          width: 280,
                          child: _GradientButton(
                            label: context.l10n.how_start_now,
                            icon: Icons.play_arrow_rounded,
                            onTap: () {
                              HapticFeedback.selectionClick();
                              Navigator.of(context).maybePop();
                              onStart?.call();
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _StepCenter extends StatelessWidget {
  const _StepCenter({
    required this.icon,
    required this.title,
    this.subtitle,
    this.iconColor,
  });
  final IconData icon;
  final String title;
  final String? subtitle;
  final Color? iconColor;

  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: cs.surface.withOpacity(0.6),
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white.withOpacity(0.2)),
          ),
          child: Icon(icon, size: 20, color: iconColor ?? cs.primary),
        ),
        const SizedBox(height: 8),
        Text(
          title,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w700),
        ),
        if (subtitle != null) ...[
          const SizedBox(height: 3),
          Text(
            subtitle!,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(color: cs.onSurfaceVariant),
          ),
        ]
      ],
    );
  }
}

class _GradientButton extends StatefulWidget {
  const _GradientButton({required this.label, required this.onTap, this.icon});
  final String label;
  final VoidCallback onTap;
  final IconData? icon;

  @override
  State<_GradientButton> createState() => _GradientButtonState();
}

class _GradientButtonState extends State<_GradientButton> with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final Animation<double> _scale;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
      lowerBound: 0,
      upperBound: 0.06,
    );
    _scale = Tween<double>(begin: 1, end: 0.94).animate(CurvedAnimation(parent: _controller, curve: Curves.easeOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;
    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapCancel: () => _controller.reverse(),
      onTapUp: (_) => _controller.reverse(),
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _scale,
        builder: (context, child) => Transform.scale(scale: _scale.value, child: child),
        child: DecoratedBox(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [cs.primary, cs.secondary],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: cs.primary.withOpacity(0.35),
                blurRadius: 26,
                offset: const Offset(0, 12),
              )
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 14.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (widget.icon != null) ...[
                  Icon(widget.icon, color: Colors.white),
                  const SizedBox(width: 6),
                ],
                Text(
                  widget.label,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
