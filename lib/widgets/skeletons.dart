import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

enum SkeletonType { personal, country }

class LeaderboardSkeletonList extends StatelessWidget {
  final SkeletonType type;
  final int count;

  const LeaderboardSkeletonList({super.key, required this.type, required this.count});

  static const double personalTileHeight = 72.0;
  static const double countryTileHeight = 72.0;

  @override
  Widget build(BuildContext context) {
    final children = List<Widget>.generate(
      count,
      (i) => SkeletonTile(type: type),
    );

    return Semantics(
      label: 'Loading',
      container: true,
      enabled: true,
      child: Column(children: children),
    );
  }
}

class SkeletonTile extends StatelessWidget {
  final SkeletonType type;
  const SkeletonTile({super.key, required this.type});

  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;
    final base = cs.surfaceVariant; // subtle base
    final highlight = cs.onSurface.withOpacity(0.06); // soft highlight

    final height = type == SkeletonType.personal
        ? LeaderboardSkeletonList.personalTileHeight
        : LeaderboardSkeletonList.countryTileHeight;

    return SizedBox(
      height: height,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Shimmer.fromColors(
          baseColor: base,
          highlightColor: highlight,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // leading avatar
              _circle(40.0, base),
              const SizedBox(width: 16),
              // text columns
              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    final w = constraints.maxWidth;
                    if (type == SkeletonType.personal) {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _bar(width: w * 0.60, height: 14.0, radius: 6.0, color: base),
                          const SizedBox(height: 8),
                          _bar(width: w * 0.40, height: 12.0, radius: 6.0, color: base),
                        ],
                      );
                    } else {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _bar(width: w * 0.70, height: 14.0, radius: 6.0, color: base),
                          const SizedBox(height: 8),
                          _bar(width: w * 0.45, height: 12.0, radius: 6.0, color: base),
                        ],
                      );
                    }
                  },
                ),
              ),
              const SizedBox(width: 12),
              if (type == SkeletonType.personal)
                _bar(width: 68.0, height: 16.0, radius: 8.0, color: base),
            ],
          ),
        ),
      ),
    );
  }

  Widget _bar({required double width, required double height, required double radius, required Color color}) {
    return ConstrainedBox(
      constraints: BoxConstraints.tightFor(width: width, height: height),
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(radius),
        ),
      ),
    );
  }

  Widget _circle(double size, Color color) {
    return SizedBox(
      width: size,
      height: size,
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
        ),
      ),
    );
  }
}
