import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'package:flutter_reaction_speed/routes/app_pages.dart';
import 'package:flutter_reaction_speed/screens/home_screen.dart';
import 'package:flutter_reaction_speed/screens/profile_screen.dart';
import 'package:flutter_reaction_speed/services/supabase_service.dart';
import 'package:flutter_reaction_speed/controller/auth_controller.dart';
import 'package:flutter_reaction_speed/controller/CountController.dart';

// l10n (generated by flutter gen-l10n)
import 'l10n/app_localizations.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: '.env');

  // Supabase init
  await Supabase.initialize(
    url: dotenv.env['SUPABASE_URL']!,
    anonKey: dotenv.env['SUPABASE_ANON_KEY']!,
  );

  // DI (GetX)
  Get.lazyPut<SupabaseService>(() => SupabaseService(), fenix: true);
  Get.lazyPut<AuthController>(() => AuthController(), fenix: true);
  Get.lazyPut<CountController>(() => CountController(), fenix: true);
  // Get.put(SettingsController(), permanent: true); // locale controller

  runApp(const App());
}

class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = ThemeData(
      colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepOrangeAccent),
      useMaterial3: true,
    );

    return GetMaterialApp(
      debugShowCheckedModeBanner: false,

      // --- i18n wiring ---
      locale: Get.locale ?? Get.deviceLocale,             // start with device locale
      fallbackLocale: const Locale('en'),                 // fallback
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ],
      supportedLocales: AppLocalizations.supportedLocales,

      // --- theme/routes ---
      theme: theme,
      darkTheme: ThemeData.dark(useMaterial3: true),
      getPages: AppPages.routes,

      // 초기 진입은 RootGate로 통일 (initialRoute와 home 동시 사용 금지)
      home: const RootGate(),
    );
  }
}

/// Locale 변경 전담 컨트롤러 (설정 화면 등에서 사용)
class SettingsController extends GetxController {
  final locale = Rxn<Locale>();
  void changeLocale(Locale l) {
    locale.value = l;
    Get.updateLocale(l); // GetMaterialApp이 자동 반영
  }
}

/// 간편 접근: context.l10n
extension L10nX on BuildContext {
  AppLocalizations get l10n => AppLocalizations.of(this)!;
}

// ---------------- Root gate ----------------
/// 앱의 첫 진입점: 프로필 국가 유무에 따라 홈/프로필로 라우팅
class RootGate extends StatelessWidget {
  const RootGate({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AuthController>(
      builder: (controller) {
        return Obx(() {
          if (controller.isLoading.isTrue) {
            return const Scaffold(body: Center(child: CircularProgressIndicator()));
          }
          return controller.hasCountry.isTrue
              ? const HomeScreen()
              : const ProfileScreen();
        });
      },
    );
  }
}
