{"appTitle": "반속: 반응속도 테스트", "home_title": "반속: 반응속도 테스트", "home_greeting": "안녕하세요, {name}!", "home_subtitle": "전 세계 유저와 반응속도를 겨뤄보세요", "home_tab_personal": "개인", "home_tab_country": "국가", "home_view_all": "전체 보기", "home_my_best": "나의 베스트", "home_global_rank": "글로벌 순위", "home_percentile": "상위", "home_tier": "티어", "home_how_to_play": "게임 방법", "home_start": "시작하기", "home_footer": "최고 반응속도에 도전하고, 국가 평균을 끌어올리세요! 🏁", "home_empty_preview": "아직 기록이 없어요. 첫 주인공이 되어보세요!", "home_tooltip_leaderboard": "리더보드", "home_tooltip_profile": "프로필", "how_title": "게임 방법", "how_step_intro": "3초 카운트다운 후 버튼이 초록으로 바뀌면 즉시 탭하세요.", "how_step_trials": "총 3번의 시도, 평균으로 티어가 정해집니다.", "how_step_board": "글로벌 리더보드는 개인 최고 기록 기준입니다.", "how_start_now": "바로 시작", "leader_title": "리더보드", "leader_tab_personal_global": "개인 · 글로벌", "leader_tab_personal_country": "개인 · 내 국가", "leader_tab_countries": "국가 랭킹", "leader_global": "글로벌", "leader_country": "국가별", "leader_loaded_all": "모든 순위를 불러왔습니다.", "leader_load_failed": "불러오기 실패: {error}", "leader_init_failed": "초기화 실패: {error}", "leader_global_load_failed": "글로벌 랭킹 불러오기 실패: {error}", "leader_local_load_failed": "국가 랭킹(개인) 불러오기 실패: {error}", "leader_country_load_failed": "국가 랭킹 불러오기 실패: {error}", "leader_prompt_set_country": "프로필에서 국가를 먼저 설정하세요.", "leader_back": "돌아가기", "leader_country_title": "{flag} {code}", "leader_avg_ms": "평균 {avg}ms", "leader_participants": "참여 {count}명", "leader_my_country": "내 국가: {code}", "leader_my_best_ms": "내 베스트: {ms}ms", "leader_my_global_rank": "내 글로벌 랭크: #{rank}", "leader_my_country_rank": "내 국가 랭크: #{rank}", "leader_country_avg_ms": "국가 평균: {ms}ms", "leader_country_median_ms": "국가 중앙값: {ms}ms", "leader_country_top_ms": "국가 최고 기록: {ms}ms", "leader_country_player_count": "참여자 수: {count}명", "result_title": "결과", "result_tier_base": "티어 산정은 3번의 반응속도 평균을 기준으로 합니다", "result_top": "상위 {percent}%", "result_current_rank": "현재 글로벌 랭크: #{rank}", "result_best_ms": "내 최고 기록: {ms}ms", "result_retry": "다시하기", "result_go_home": "홈으로", "result_open_leaderboard": "리더보드 보기", "result_saving": "기록을 저장하는 중...", "result_try1": "1st: {ms}", "result_try2": "2nd: {ms}", "result_try3": "3rd: {ms}", "result_average_ms": "평균 : {ms}ms", "test_title": "반응 속도 테스트", "test_sound_on": "사운드 끄기", "test_sound_off": "사운드 켜기", "test_haptics_on": "햅틱 끄기", "test_haptics_off": "햅틱 켜기", "test_wait": "기다리세요…", "test_tap_now": "지금 탭!", "test_too_fast": "너무 빨라요", "test_too_soon_hint": "너무 빨라요! 초록색이 된 후 탭하세요", "test_hint_countdown": "3초 뒤 시작합니다", "test_hint_waiting": "버튼이 초록색으로 바뀌면 터치하세요", "test_hint_green": "지금! 최대한 빨리 탭!", "test_hint_lockout": "너무 빨랐어요 — 재시작합니다", "test_try1_short": "1st", "test_try2_short": "2nd", "test_try3_short": "3rd", "profile_title": "프로필", "profile_country_label": "국가", "profile_country_hint": "국가를 선택하세요", "profile_unset": "미설정", "profile_pick": "선택", "profile_display_name_label": "표시 이름 (리더보드에 표시됩니다)", "profile_display_name_autogen": "생성 중... 저장 시 자동 생성됩니다", "profile_save": "저장", "profile_saved_title": "프로필", "profile_saved_message": "저장되었습니다.", "profile_error_title": "오류", "profile_save_failed": "저장 실패: {error}", "profile_load_failed": "프로필 정보를 불러오는 데 실패했습니다: {error}", "profile_user_id": "사용자 ID: {id}", "common_unknown": "알 수 없음", "common_anonymous": "익명", "common_error": "오류", "common_profile_load_failed_fallback": "프로필 정보를 불러오는 데 실패했습니다: {error}", "common_close": "닫기", "common_notice": "알림", "country_code": "{code}", "@home_greeting": {"placeholders": {"name": {"type": "String"}}}, "@leader_load_failed": {"placeholders": {"error": {"type": "String"}}}, "@leader_init_failed": {"placeholders": {"error": {"type": "String"}}}, "@leader_global_load_failed": {"placeholders": {"error": {"type": "String"}}}, "@leader_local_load_failed": {"placeholders": {"error": {"type": "String"}}}, "@leader_country_load_failed": {"placeholders": {"error": {"type": "String"}}}, "@leader_country_title": {"placeholders": {"flag": {"type": "String"}, "code": {"type": "String"}}}, "@leader_avg_ms": {"placeholders": {"avg": {"type": "String"}}}, "@leader_participants": {"placeholders": {"count": {"type": "String"}}}, "@leader_my_country": {"placeholders": {"code": {"type": "String"}}}, "@leader_my_best_ms": {"placeholders": {"ms": {"type": "String"}}}, "@leader_my_global_rank": {"placeholders": {"rank": {"type": "String"}}}, "@leader_my_country_rank": {"placeholders": {"rank": {"type": "String"}}}, "@leader_country_avg_ms": {"placeholders": {"ms": {"type": "String"}}}, "@leader_country_median_ms": {"placeholders": {"ms": {"type": "String"}}}, "@leader_country_top_ms": {"placeholders": {"ms": {"type": "String"}}}, "@leader_country_player_count": {"placeholders": {"count": {"type": "String"}}}, "@result_top": {"placeholders": {"percent": {"type": "num"}}}, "@result_current_rank": {"placeholders": {"rank": {"type": "int"}}}, "@result_best_ms": {"placeholders": {"ms": {"type": "int"}}}, "@result_try1": {"placeholders": {"ms": {"type": "String"}}}, "@result_try2": {"placeholders": {"ms": {"type": "String"}}}, "@result_try3": {"placeholders": {"ms": {"type": "String"}}}, "@result_average_ms": {"placeholders": {"ms": {"type": "String"}}}, "@profile_save_failed": {"placeholders": {"error": {"type": "String"}}}, "@profile_load_failed": {"placeholders": {"error": {"type": "String"}}}, "@profile_user_id": {"placeholders": {"id": {"type": "String"}}}, "@common_profile_load_failed_fallback": {"placeholders": {"error": {"type": "String"}}}}