{"appTitle": "Reaction Speed Test", "home_title": "Reaction Speed Test", "home_greeting": "Hello, {name}!", "home_subtitle": "Challenge your reaction speed with players worldwide", "home_tab_personal": "Personal", "home_tab_country": "Country", "home_view_all": "View all", "home_my_best": "My Best", "home_global_rank": "Global Rank", "home_percentile": "Top", "home_tier": "Tier", "home_how_to_play": "How to play", "home_start": "Start", "home_footer": "Go for your best and lift your nation's average! 🏁", "home_empty_preview": "No records yet. Be the first!", "home_tooltip_leaderboard": "Leaderboard", "home_tooltip_profile": "Profile", "how_title": "How to play", "how_step_intro": "After a 3s countdown, tap the button as soon as it turns green.", "how_step_trials": "You get 3 trials. Your tier is based on the average.", "how_step_board": "Global leaderboard is based on personal best.", "how_start_now": "Start now", "leader_title": "Leaderboard", "leader_tab_personal_global": "Personal · Global", "leader_tab_personal_country": "Personal · My country", "leader_tab_countries": "Country ranks", "leader_global": "Global", "leader_country": "By Country", "leader_loaded_all": "All ranks loaded.", "leader_load_failed": "Failed to load: {error}", "leader_init_failed": "Init failed: {error}", "leader_prompt_set_country": "Please set your country in Profile first.", "leader_back": "Back", "leader_country_title": "{flag} {code}", "leader_avg_ms": "Avg {avg} ms", "leader_participants": "Participants {count}", "leader_my_country": "My country: {code}", "leader_my_best_ms": "My best: {ms} ms", "leader_my_global_rank": "My global rank: #{rank}", "leader_my_country_rank": "My country rank: #{rank}", "leader_country_avg_ms": "Country average: {ms} ms", "leader_country_median_ms": "Country median: {ms} ms", "leader_country_top_ms": "Country best: {ms} ms", "leader_country_player_count": "Players: {count}", "result_title": "Result", "result_tier_base": "Tier is based on the average of 3 trials", "result_top": "Top {percent}%", "result_current_rank": "Current global rank: #{rank}", "result_best_ms": "Your best: {ms} ms", "result_retry": "Retry", "result_go_home": "Home", "result_open_leaderboard": "Open leaderboard", "result_saving": "Saving your records...", "result_try1": "1st: {ms}", "result_try2": "2nd: {ms}", "result_try3": "3rd: {ms}", "result_average_ms": "Average: {ms} ms", "test_title": "Reaction Speed Test", "test_sound_on": "Turn sound off", "test_sound_off": "Turn sound on", "test_haptics_on": "Turn haptics off", "test_haptics_off": "Turn haptics on", "test_wait": "Wait…", "test_tap_now": "Tap now!", "test_too_fast": "Too soon", "test_too_soon_hint": "Too soon! Tap after it turns green.", "test_hint_countdown": "Starting in 3 seconds", "test_hint_waiting": "Tap when the button turns green", "test_hint_green": "Now! Tap as fast as you can!", "test_hint_lockout": "Too soon — restarting", "test_try1_short": "1st", "test_try2_short": "2nd", "test_try3_short": "3rd", "profile_title": "Profile", "profile_country_label": "Country", "profile_country_hint": "Select your country", "profile_unset": "Not set", "profile_pick": "Pick", "profile_display_name_label": "Display name (shown on leaderboard)", "profile_display_name_autogen": "Generating... It will be auto-created on save", "profile_save": "Save", "profile_saved_title": "Profile", "profile_saved_message": "Saved.", "profile_error_title": "Error", "profile_save_failed": "Save failed: {error}", "profile_user_id": "User ID: {id}", "common_unknown": "Unknown", "common_close": "Close", "common_notice": "Notice", "country_code": "{code}", "@home_greeting": {"placeholders": {"name": {"type": "String"}}}, "@leader_load_failed": {"placeholders": {"error": {"type": "String"}}}, "@leader_init_failed": {"placeholders": {"error": {"type": "String"}}}, "@leader_country_title": {"placeholders": {"flag": {"type": "String"}, "code": {"type": "String"}}}, "@leader_avg_ms": {"placeholders": {"avg": {"type": "String"}}}, "@leader_participants": {"placeholders": {"count": {"type": "String"}}}, "@leader_my_country": {"placeholders": {"code": {"type": "String"}}}, "@leader_my_best_ms": {"placeholders": {"ms": {"type": "String"}}}, "@leader_my_global_rank": {"placeholders": {"rank": {"type": "String"}}}, "@leader_my_country_rank": {"placeholders": {"rank": {"type": "String"}}}, "@leader_country_avg_ms": {"placeholders": {"ms": {"type": "String"}}}, "@leader_country_median_ms": {"placeholders": {"ms": {"type": "String"}}}, "@leader_country_top_ms": {"placeholders": {"ms": {"type": "String"}}}, "@leader_country_player_count": {"placeholders": {"count": {"type": "String"}}}, "@result_top": {"placeholders": {"percent": {"type": "num"}}}, "@result_current_rank": {"placeholders": {"rank": {"type": "int"}}}, "@result_best_ms": {"placeholders": {"ms": {"type": "int"}}}, "@result_try1": {"placeholders": {"ms": {"type": "String"}}}, "@result_try2": {"placeholders": {"ms": {"type": "String"}}}, "@result_try3": {"placeholders": {"ms": {"type": "String"}}}, "@result_average_ms": {"placeholders": {"ms": {"type": "String"}}}, "@profile_save_failed": {"placeholders": {"error": {"type": "String"}}}, "@profile_user_id": {"placeholders": {"id": {"type": "String"}}}}