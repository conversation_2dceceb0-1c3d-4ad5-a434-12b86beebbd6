// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Japanese (`ja`).
class AppLocalizationsJa extends AppLocalizations {
  AppLocalizationsJa([String locale = 'ja']) : super(locale);

  @override
  String get appTitle => '反応速度テスト';

  @override
  String get home_title => '反応速度テスト';

  @override
  String home_greeting(String name) {
    return 'こんにちは、$nameさん!';
  }

  @override
  String get home_subtitle => '世界中のプレイヤーと反応速度を競いましょう';

  @override
  String get home_tab_personal => '個人';

  @override
  String get home_tab_country => '国別';

  @override
  String get home_view_all => 'すべて表示';

  @override
  String get home_my_best => '自己ベスト';

  @override
  String get home_global_rank => '世界ランク';

  @override
  String get home_percentile => '上位';

  @override
  String get home_tier => 'ティア';

  @override
  String get home_how_to_play => '遊び方';

  @override
  String get home_start => 'スタート';

  @override
  String get home_footer => 'ベストを目指して、国の平均も引き上げよう！ 🏁';

  @override
  String get home_empty_preview => 'まだ記録がありません。最初の栄冠を掴もう！';

  @override
  String get home_tooltip_leaderboard => 'リーダーボード';

  @override
  String get home_tooltip_profile => 'プロフィール';

  @override
  String get how_title => '遊び方';

  @override
  String get how_step_intro => '3秒のカウント後、ボタンが緑になったらすぐにタップ。';

  @override
  String get how_step_trials => '3回の試行、平均でティアが決まります。';

  @override
  String get how_step_board => 'グローバル順位は個人のベスト記録で決定。';

  @override
  String get how_start_now => '今すぐ開始';

  @override
  String get leader_title => 'リーダーボード';

  @override
  String get leader_tab_personal_global => '個人・グローバル';

  @override
  String get leader_tab_personal_country => '個人・自国';

  @override
  String get leader_tab_countries => '国別ランキング';

  @override
  String get leader_global => 'グローバル';

  @override
  String get leader_country => '国別';

  @override
  String get leader_loaded_all => 'すべての順位を読み込みました。';

  @override
  String leader_load_failed(String error) {
    return '読み込み失敗: $error';
  }

  @override
  String leader_init_failed(String error) {
    return '初期化失敗: $error';
  }

  @override
  String leader_global_load_failed(String error) {
    return 'グローバルランキングの読み込みに失敗しました: $error';
  }

  @override
  String leader_local_load_failed(String error) {
    return '国別ランキング（個人）の読み込みに失敗しました: $error';
  }

  @override
  String leader_country_load_failed(String error) {
    return '国別ランキングの読み込みに失敗しました: $error';
  }

  @override
  String get leader_prompt_set_country => 'プロフィールで国を設定してください。';

  @override
  String get leader_back => '戻る';

  @override
  String leader_country_title(String flag, String code) {
    return '$flag $code';
  }

  @override
  String leader_avg_ms(String avg) {
    return '平均 ${avg}ms';
  }

  @override
  String leader_participants(String count) {
    return '参加者 $count人';
  }

  @override
  String leader_my_country(String code) {
    return '自国: $code';
  }

  @override
  String leader_my_best_ms(String ms) {
    return '自己ベスト: ${ms}ms';
  }

  @override
  String leader_my_global_rank(String rank) {
    return '世界ランク: #$rank';
  }

  @override
  String leader_my_country_rank(String rank) {
    return '国内ランク: #$rank';
  }

  @override
  String leader_country_avg_ms(String ms) {
    return '国の平均: ${ms}ms';
  }

  @override
  String leader_country_median_ms(String ms) {
    return '国の中央値: ${ms}ms';
  }

  @override
  String leader_country_top_ms(String ms) {
    return '国の最高: ${ms}ms';
  }

  @override
  String leader_country_player_count(String count) {
    return 'プレイヤー数: $count';
  }

  @override
  String get result_title => '結果';

  @override
  String get result_tier_base => 'ティアは3回の平均で算出';

  @override
  String result_top(num percent) {
    return '上位 $percent%';
  }

  @override
  String result_current_rank(int rank) {
    return '現在の世界ランク: #$rank';
  }

  @override
  String result_best_ms(int ms) {
    return '自己ベスト: ${ms}ms';
  }

  @override
  String get result_retry => 'リトライ';

  @override
  String get result_go_home => 'ホームへ';

  @override
  String get result_open_leaderboard => 'リーダーボード';

  @override
  String get result_saving => '記録を保存中…';

  @override
  String result_try1(String ms) {
    return '1st: $ms';
  }

  @override
  String result_try2(String ms) {
    return '2nd: $ms';
  }

  @override
  String result_try3(String ms) {
    return '3rd: $ms';
  }

  @override
  String result_average_ms(String ms) {
    return '平均: ${ms}ms';
  }

  @override
  String get test_title => '反応速度テスト';

  @override
  String get test_sound_on => 'サウンドをオフ';

  @override
  String get test_sound_off => 'サウンドをオン';

  @override
  String get test_haptics_on => 'ハプティクスをオフ';

  @override
  String get test_haptics_off => 'ハプティクスをオン';

  @override
  String get test_wait => 'お待ちください…';

  @override
  String get test_tap_now => '今タップ！';

  @override
  String get test_too_fast => '早すぎます';

  @override
  String get test_too_soon_hint => '早すぎます！緑になってからタップしてください。';

  @override
  String get test_hint_countdown => '3秒後に開始します';

  @override
  String get test_hint_waiting => 'ボタンが緑になったらタップ';

  @override
  String get test_hint_green => '今！できるだけ早くタップ！';

  @override
  String get test_hint_lockout => '早すぎました — 再開します';

  @override
  String get test_try1_short => '1st';

  @override
  String get test_try2_short => '2nd';

  @override
  String get test_try3_short => '3rd';

  @override
  String get profile_title => 'プロフィール';

  @override
  String get profile_country_label => '国';

  @override
  String get profile_country_hint => '国を選択してください';

  @override
  String get profile_unset => '未設定';

  @override
  String get profile_pick => '選択';

  @override
  String get profile_display_name_label => '表示名（リーダーボードに表示）';

  @override
  String get profile_display_name_autogen => '作成中… 保存時に自動生成されます';

  @override
  String get profile_save => '保存';

  @override
  String get profile_saved_title => 'プロフィール';

  @override
  String get profile_saved_message => '保存しました。';

  @override
  String get profile_error_title => 'エラー';

  @override
  String profile_save_failed(String error) {
    return '保存に失敗しました: $error';
  }

  @override
  String profile_load_failed(String error) {
    return 'プロフィールの読み込みに失敗しました: $error';
  }

  @override
  String profile_user_id(String id) {
    return 'ユーザーID: $id';
  }

  @override
  String get common_unknown => '不明';

  @override
  String get common_anonymous => '匿名';

  @override
  String get common_error => 'エラー';

  @override
  String common_profile_load_failed_fallback(String error) {
    return 'プロフィールの読み込みに失敗しました: $error';
  }

  @override
  String get common_close => '閉じる';

  @override
  String get common_notice => 'お知らせ';

  @override
  String country_code(Object code) {
    return '$code';
  }
}
