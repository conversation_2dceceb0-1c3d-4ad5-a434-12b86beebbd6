import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_ja.dart';
import 'app_localizations_ko.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('ja'),
    Locale('ko')
  ];

  /// No description provided for @appTitle.
  ///
  /// In en, this message translates to:
  /// **'Reaction Speed Test'**
  String get appTitle;

  /// No description provided for @home_title.
  ///
  /// In en, this message translates to:
  /// **'Reaction Speed Test'**
  String get home_title;

  /// No description provided for @home_greeting.
  ///
  /// In en, this message translates to:
  /// **'Hello, {name}!'**
  String home_greeting(String name);

  /// No description provided for @home_subtitle.
  ///
  /// In en, this message translates to:
  /// **'Challenge your reaction speed with players worldwide'**
  String get home_subtitle;

  /// No description provided for @home_tab_personal.
  ///
  /// In en, this message translates to:
  /// **'Personal'**
  String get home_tab_personal;

  /// No description provided for @home_tab_country.
  ///
  /// In en, this message translates to:
  /// **'Country'**
  String get home_tab_country;

  /// No description provided for @home_view_all.
  ///
  /// In en, this message translates to:
  /// **'View all'**
  String get home_view_all;

  /// No description provided for @home_my_best.
  ///
  /// In en, this message translates to:
  /// **'My Best'**
  String get home_my_best;

  /// No description provided for @home_global_rank.
  ///
  /// In en, this message translates to:
  /// **'Global Rank'**
  String get home_global_rank;

  /// No description provided for @home_percentile.
  ///
  /// In en, this message translates to:
  /// **'Top'**
  String get home_percentile;

  /// No description provided for @home_tier.
  ///
  /// In en, this message translates to:
  /// **'Tier'**
  String get home_tier;

  /// No description provided for @home_how_to_play.
  ///
  /// In en, this message translates to:
  /// **'How to play'**
  String get home_how_to_play;

  /// No description provided for @home_start.
  ///
  /// In en, this message translates to:
  /// **'Start'**
  String get home_start;

  /// No description provided for @home_footer.
  ///
  /// In en, this message translates to:
  /// **'Go for your best and lift your nation\'s average! 🏁'**
  String get home_footer;

  /// No description provided for @home_empty_preview.
  ///
  /// In en, this message translates to:
  /// **'No records yet. Be the first!'**
  String get home_empty_preview;

  /// No description provided for @home_tooltip_leaderboard.
  ///
  /// In en, this message translates to:
  /// **'Leaderboard'**
  String get home_tooltip_leaderboard;

  /// No description provided for @home_tooltip_profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get home_tooltip_profile;

  /// No description provided for @how_title.
  ///
  /// In en, this message translates to:
  /// **'How to play'**
  String get how_title;

  /// No description provided for @how_step_intro.
  ///
  /// In en, this message translates to:
  /// **'After a 3s countdown, tap the button as soon as it turns green.'**
  String get how_step_intro;

  /// No description provided for @how_step_trials.
  ///
  /// In en, this message translates to:
  /// **'You get 3 trials. Your tier is based on the average.'**
  String get how_step_trials;

  /// No description provided for @how_step_board.
  ///
  /// In en, this message translates to:
  /// **'Global leaderboard is based on personal best.'**
  String get how_step_board;

  /// No description provided for @how_start_now.
  ///
  /// In en, this message translates to:
  /// **'Start now'**
  String get how_start_now;

  /// No description provided for @leader_title.
  ///
  /// In en, this message translates to:
  /// **'Leaderboard'**
  String get leader_title;

  /// No description provided for @leader_tab_personal_global.
  ///
  /// In en, this message translates to:
  /// **'Personal · Global'**
  String get leader_tab_personal_global;

  /// No description provided for @leader_tab_personal_country.
  ///
  /// In en, this message translates to:
  /// **'Personal · My country'**
  String get leader_tab_personal_country;

  /// No description provided for @leader_tab_countries.
  ///
  /// In en, this message translates to:
  /// **'Country ranks'**
  String get leader_tab_countries;

  /// No description provided for @leader_global.
  ///
  /// In en, this message translates to:
  /// **'Global'**
  String get leader_global;

  /// No description provided for @leader_country.
  ///
  /// In en, this message translates to:
  /// **'By Country'**
  String get leader_country;

  /// No description provided for @leader_loaded_all.
  ///
  /// In en, this message translates to:
  /// **'All ranks loaded.'**
  String get leader_loaded_all;

  /// No description provided for @leader_load_failed.
  ///
  /// In en, this message translates to:
  /// **'Failed to load: {error}'**
  String leader_load_failed(String error);

  /// No description provided for @leader_init_failed.
  ///
  /// In en, this message translates to:
  /// **'Init failed: {error}'**
  String leader_init_failed(String error);

  /// No description provided for @leader_prompt_set_country.
  ///
  /// In en, this message translates to:
  /// **'Please set your country in Profile first.'**
  String get leader_prompt_set_country;

  /// No description provided for @leader_back.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get leader_back;

  /// No description provided for @leader_country_title.
  ///
  /// In en, this message translates to:
  /// **'{flag} {code}'**
  String leader_country_title(String flag, String code);

  /// No description provided for @leader_avg_ms.
  ///
  /// In en, this message translates to:
  /// **'Avg {avg} ms'**
  String leader_avg_ms(String avg);

  /// No description provided for @leader_participants.
  ///
  /// In en, this message translates to:
  /// **'Participants {count}'**
  String leader_participants(String count);

  /// No description provided for @leader_my_country.
  ///
  /// In en, this message translates to:
  /// **'My country: {code}'**
  String leader_my_country(String code);

  /// No description provided for @leader_my_best_ms.
  ///
  /// In en, this message translates to:
  /// **'My best: {ms} ms'**
  String leader_my_best_ms(String ms);

  /// No description provided for @leader_my_global_rank.
  ///
  /// In en, this message translates to:
  /// **'My global rank: #{rank}'**
  String leader_my_global_rank(String rank);

  /// No description provided for @leader_my_country_rank.
  ///
  /// In en, this message translates to:
  /// **'My country rank: #{rank}'**
  String leader_my_country_rank(String rank);

  /// No description provided for @leader_country_avg_ms.
  ///
  /// In en, this message translates to:
  /// **'Country average: {ms} ms'**
  String leader_country_avg_ms(String ms);

  /// No description provided for @leader_country_median_ms.
  ///
  /// In en, this message translates to:
  /// **'Country median: {ms} ms'**
  String leader_country_median_ms(String ms);

  /// No description provided for @leader_country_top_ms.
  ///
  /// In en, this message translates to:
  /// **'Country best: {ms} ms'**
  String leader_country_top_ms(String ms);

  /// No description provided for @leader_country_player_count.
  ///
  /// In en, this message translates to:
  /// **'Players: {count}'**
  String leader_country_player_count(String count);

  /// No description provided for @result_title.
  ///
  /// In en, this message translates to:
  /// **'Result'**
  String get result_title;

  /// No description provided for @result_tier_base.
  ///
  /// In en, this message translates to:
  /// **'Tier is based on the average of 3 trials'**
  String get result_tier_base;

  /// No description provided for @result_top.
  ///
  /// In en, this message translates to:
  /// **'Top {percent}%'**
  String result_top(num percent);

  /// No description provided for @result_current_rank.
  ///
  /// In en, this message translates to:
  /// **'Current global rank: #{rank}'**
  String result_current_rank(int rank);

  /// No description provided for @result_best_ms.
  ///
  /// In en, this message translates to:
  /// **'Your best: {ms} ms'**
  String result_best_ms(int ms);

  /// No description provided for @result_retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get result_retry;

  /// No description provided for @result_go_home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get result_go_home;

  /// No description provided for @result_open_leaderboard.
  ///
  /// In en, this message translates to:
  /// **'Open leaderboard'**
  String get result_open_leaderboard;

  /// No description provided for @result_saving.
  ///
  /// In en, this message translates to:
  /// **'Saving your records...'**
  String get result_saving;

  /// No description provided for @result_try1.
  ///
  /// In en, this message translates to:
  /// **'1st: {ms}'**
  String result_try1(String ms);

  /// No description provided for @result_try2.
  ///
  /// In en, this message translates to:
  /// **'2nd: {ms}'**
  String result_try2(String ms);

  /// No description provided for @result_try3.
  ///
  /// In en, this message translates to:
  /// **'3rd: {ms}'**
  String result_try3(String ms);

  /// No description provided for @result_average_ms.
  ///
  /// In en, this message translates to:
  /// **'Average: {ms} ms'**
  String result_average_ms(String ms);

  /// No description provided for @test_title.
  ///
  /// In en, this message translates to:
  /// **'Reaction Speed Test'**
  String get test_title;

  /// No description provided for @test_sound_on.
  ///
  /// In en, this message translates to:
  /// **'Turn sound off'**
  String get test_sound_on;

  /// No description provided for @test_sound_off.
  ///
  /// In en, this message translates to:
  /// **'Turn sound on'**
  String get test_sound_off;

  /// No description provided for @test_haptics_on.
  ///
  /// In en, this message translates to:
  /// **'Turn haptics off'**
  String get test_haptics_on;

  /// No description provided for @test_haptics_off.
  ///
  /// In en, this message translates to:
  /// **'Turn haptics on'**
  String get test_haptics_off;

  /// No description provided for @test_wait.
  ///
  /// In en, this message translates to:
  /// **'Wait…'**
  String get test_wait;

  /// No description provided for @test_tap_now.
  ///
  /// In en, this message translates to:
  /// **'Tap now!'**
  String get test_tap_now;

  /// No description provided for @test_too_fast.
  ///
  /// In en, this message translates to:
  /// **'Too soon'**
  String get test_too_fast;

  /// No description provided for @test_too_soon_hint.
  ///
  /// In en, this message translates to:
  /// **'Too soon! Tap after it turns green.'**
  String get test_too_soon_hint;

  /// No description provided for @test_hint_countdown.
  ///
  /// In en, this message translates to:
  /// **'Starting in 3 seconds'**
  String get test_hint_countdown;

  /// No description provided for @test_hint_waiting.
  ///
  /// In en, this message translates to:
  /// **'Tap when the button turns green'**
  String get test_hint_waiting;

  /// No description provided for @test_hint_green.
  ///
  /// In en, this message translates to:
  /// **'Now! Tap as fast as you can!'**
  String get test_hint_green;

  /// No description provided for @test_hint_lockout.
  ///
  /// In en, this message translates to:
  /// **'Too soon — restarting'**
  String get test_hint_lockout;

  /// No description provided for @test_try1_short.
  ///
  /// In en, this message translates to:
  /// **'1st'**
  String get test_try1_short;

  /// No description provided for @test_try2_short.
  ///
  /// In en, this message translates to:
  /// **'2nd'**
  String get test_try2_short;

  /// No description provided for @test_try3_short.
  ///
  /// In en, this message translates to:
  /// **'3rd'**
  String get test_try3_short;

  /// No description provided for @profile_title.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile_title;

  /// No description provided for @profile_country_label.
  ///
  /// In en, this message translates to:
  /// **'Country'**
  String get profile_country_label;

  /// No description provided for @profile_country_hint.
  ///
  /// In en, this message translates to:
  /// **'Select your country'**
  String get profile_country_hint;

  /// No description provided for @profile_unset.
  ///
  /// In en, this message translates to:
  /// **'Not set'**
  String get profile_unset;

  /// No description provided for @profile_pick.
  ///
  /// In en, this message translates to:
  /// **'Pick'**
  String get profile_pick;

  /// No description provided for @profile_display_name_label.
  ///
  /// In en, this message translates to:
  /// **'Display name (shown on leaderboard)'**
  String get profile_display_name_label;

  /// No description provided for @profile_display_name_autogen.
  ///
  /// In en, this message translates to:
  /// **'Generating... It will be auto-created on save'**
  String get profile_display_name_autogen;

  /// No description provided for @profile_save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get profile_save;

  /// No description provided for @profile_saved_title.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile_saved_title;

  /// No description provided for @profile_saved_message.
  ///
  /// In en, this message translates to:
  /// **'Saved.'**
  String get profile_saved_message;

  /// No description provided for @profile_error_title.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get profile_error_title;

  /// No description provided for @profile_save_failed.
  ///
  /// In en, this message translates to:
  /// **'Save failed: {error}'**
  String profile_save_failed(String error);

  /// No description provided for @profile_load_failed.
  ///
  /// In en, this message translates to:
  /// **'Failed to load profile: {error}'**
  String profile_load_failed(String error);

  /// No description provided for @profile_user_id.
  ///
  /// In en, this message translates to:
  /// **'User ID: {id}'**
  String profile_user_id(String id);

  /// No description provided for @common_unknown.
  ///
  /// In en, this message translates to:
  /// **'Unknown'**
  String get common_unknown;

  /// No description provided for @common_close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get common_close;

  /// No description provided for @common_notice.
  ///
  /// In en, this message translates to:
  /// **'Notice'**
  String get common_notice;

  /// No description provided for @country_code.
  ///
  /// In en, this message translates to:
  /// **'{code}'**
  String country_code(Object code);
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'ja', 'ko'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'ja': return AppLocalizationsJa();
    case 'ko': return AppLocalizationsKo();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
