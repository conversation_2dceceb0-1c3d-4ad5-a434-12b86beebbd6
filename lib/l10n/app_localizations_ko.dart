// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Korean (`ko`).
class AppLocalizationsKo extends AppLocalizations {
  AppLocalizationsKo([String locale = 'ko']) : super(locale);

  @override
  String get appTitle => '반속: 반응속도 테스트';

  @override
  String get home_title => '반속: 반응속도 테스트';

  @override
  String home_greeting(String name) {
    return '안녕하세요, $name!';
  }

  @override
  String get home_subtitle => '전 세계 유저와 반응속도를 겨뤄보세요';

  @override
  String get home_tab_personal => '개인';

  @override
  String get home_tab_country => '국가';

  @override
  String get home_view_all => '전체 보기';

  @override
  String get home_my_best => '나의 베스트';

  @override
  String get home_global_rank => '글로벌 순위';

  @override
  String get home_percentile => '상위';

  @override
  String get home_tier => '티어';

  @override
  String get home_how_to_play => '게임 방법';

  @override
  String get home_start => '시작하기';

  @override
  String get home_footer => '최고 반응속도에 도전하고, 국가 평균을 끌어올리세요! 🏁';

  @override
  String get home_empty_preview => '아직 기록이 없어요. 첫 주인공이 되어보세요!';

  @override
  String get home_tooltip_leaderboard => '리더보드';

  @override
  String get home_tooltip_profile => '프로필';

  @override
  String get how_title => '게임 방법';

  @override
  String get how_step_intro => '3초 카운트다운 후 버튼이 초록으로 바뀌면 즉시 탭하세요.';

  @override
  String get how_step_trials => '총 3번의 시도, 평균으로 티어가 정해집니다.';

  @override
  String get how_step_board => '글로벌 리더보드는 개인 최고 기록 기준입니다.';

  @override
  String get how_start_now => '바로 시작';

  @override
  String get leader_title => '리더보드';

  @override
  String get leader_tab_personal_global => '개인 · 글로벌';

  @override
  String get leader_tab_personal_country => '개인 · 내 국가';

  @override
  String get leader_tab_countries => '국가 랭킹';

  @override
  String get leader_global => '글로벌';

  @override
  String get leader_country => '국가별';

  @override
  String get leader_loaded_all => '모든 순위를 불러왔습니다.';

  @override
  String leader_load_failed(String error) {
    return '불러오기 실패: $error';
  }

  @override
  String leader_init_failed(String error) {
    return '초기화 실패: $error';
  }

  @override
  String get leader_prompt_set_country => '프로필에서 국가를 먼저 설정하세요.';

  @override
  String get leader_back => '돌아가기';

  @override
  String leader_country_title(String flag, String code) {
    return '$flag $code';
  }

  @override
  String leader_avg_ms(String avg) {
    return '평균 ${avg}ms';
  }

  @override
  String leader_participants(String count) {
    return '참여 $count명';
  }

  @override
  String leader_my_country(String code) {
    return '내 국가: $code';
  }

  @override
  String leader_my_best_ms(String ms) {
    return '내 베스트: ${ms}ms';
  }

  @override
  String leader_my_global_rank(String rank) {
    return '내 글로벌 랭크: #$rank';
  }

  @override
  String leader_my_country_rank(String rank) {
    return '내 국가 랭크: #$rank';
  }

  @override
  String leader_country_avg_ms(String ms) {
    return '국가 평균: ${ms}ms';
  }

  @override
  String leader_country_median_ms(String ms) {
    return '국가 중앙값: ${ms}ms';
  }

  @override
  String leader_country_top_ms(String ms) {
    return '국가 최고 기록: ${ms}ms';
  }

  @override
  String leader_country_player_count(String count) {
    return '참여자 수: $count명';
  }

  @override
  String get result_title => '결과';

  @override
  String get result_tier_base => '티어 산정은 3번의 반응속도 평균을 기준으로 합니다';

  @override
  String result_top(num percent) {
    return '상위 $percent%';
  }

  @override
  String result_current_rank(int rank) {
    return '현재 글로벌 랭크: #$rank';
  }

  @override
  String result_best_ms(int ms) {
    return '내 최고 기록: ${ms}ms';
  }

  @override
  String get result_retry => '다시하기';

  @override
  String get result_go_home => '홈으로';

  @override
  String get result_open_leaderboard => '리더보드 보기';

  @override
  String get result_saving => '기록을 저장하는 중...';

  @override
  String result_try1(String ms) {
    return '1st: $ms';
  }

  @override
  String result_try2(String ms) {
    return '2nd: $ms';
  }

  @override
  String result_try3(String ms) {
    return '3rd: $ms';
  }

  @override
  String result_average_ms(String ms) {
    return '평균 : ${ms}ms';
  }

  @override
  String get test_title => '반응 속도 테스트';

  @override
  String get test_sound_on => '사운드 끄기';

  @override
  String get test_sound_off => '사운드 켜기';

  @override
  String get test_haptics_on => '햅틱 끄기';

  @override
  String get test_haptics_off => '햅틱 켜기';

  @override
  String get test_wait => '기다리세요…';

  @override
  String get test_tap_now => '지금 탭!';

  @override
  String get test_too_fast => '너무 빨라요';

  @override
  String get test_too_soon_hint => '너무 빨라요! 초록색이 된 후 탭하세요';

  @override
  String get test_hint_countdown => '3초 뒤 시작합니다';

  @override
  String get test_hint_waiting => '버튼이 초록색으로 바뀌면 터치하세요';

  @override
  String get test_hint_green => '지금! 최대한 빨리 탭!';

  @override
  String get test_hint_lockout => '너무 빨랐어요 — 재시작합니다';

  @override
  String get test_try1_short => '1st';

  @override
  String get test_try2_short => '2nd';

  @override
  String get test_try3_short => '3rd';

  @override
  String get profile_title => '프로필';

  @override
  String get profile_country_label => '국가';

  @override
  String get profile_country_hint => '국가를 선택하세요';

  @override
  String get profile_unset => '미설정';

  @override
  String get profile_pick => '선택';

  @override
  String get profile_display_name_label => '표시 이름 (리더보드에 표시됩니다)';

  @override
  String get profile_display_name_autogen => '생성 중... 저장 시 자동 생성됩니다';

  @override
  String get profile_save => '저장';

  @override
  String get profile_saved_title => '프로필';

  @override
  String get profile_saved_message => '저장되었습니다.';

  @override
  String get profile_error_title => '오류';

  @override
  String profile_save_failed(String error) {
    return '저장 실패: $error';
  }

  @override
  String profile_user_id(String id) {
    return '사용자 ID: $id';
  }

  @override
  String get common_unknown => '알 수 없음';

  @override
  String get common_close => '닫기';

  @override
  String get common_notice => '알림';

  @override
  String country_code(Object code) {
    return '$code';
  }
}
