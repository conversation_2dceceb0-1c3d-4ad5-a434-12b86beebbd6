// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Reaction Speed Test';

  @override
  String get home_title => 'Reaction Speed Test';

  @override
  String home_greeting(String name) {
    return 'Hello, $name!';
  }

  @override
  String get home_subtitle => 'Challenge your reaction speed with players worldwide';

  @override
  String get home_tab_personal => 'Personal';

  @override
  String get home_tab_country => 'Country';

  @override
  String get home_view_all => 'View all';

  @override
  String get home_my_best => 'My Best';

  @override
  String get home_global_rank => 'Global Rank';

  @override
  String get home_percentile => 'Top';

  @override
  String get home_tier => 'Tier';

  @override
  String get home_how_to_play => 'How to play';

  @override
  String get home_start => 'Start';

  @override
  String get home_footer => 'Go for your best and lift your nation\'s average! 🏁';

  @override
  String get home_empty_preview => 'No records yet. Be the first!';

  @override
  String get home_tooltip_leaderboard => 'Leaderboard';

  @override
  String get home_tooltip_profile => 'Profile';

  @override
  String get how_title => 'How to play';

  @override
  String get how_step_intro => 'After a 3s countdown, tap the button as soon as it turns green.';

  @override
  String get how_step_trials => 'You get 3 trials. Your tier is based on the average.';

  @override
  String get how_step_board => 'Global leaderboard is based on personal best.';

  @override
  String get how_start_now => 'Start now';

  @override
  String get leader_title => 'Leaderboard';

  @override
  String get leader_tab_personal_global => 'Personal · Global';

  @override
  String get leader_tab_personal_country => 'Personal · My country';

  @override
  String get leader_tab_countries => 'Country ranks';

  @override
  String get leader_global => 'Global';

  @override
  String get leader_country => 'By Country';

  @override
  String get leader_loaded_all => 'All ranks loaded.';

  @override
  String leader_load_failed(String error) {
    return 'Failed to load: $error';
  }

  @override
  String leader_init_failed(String error) {
    return 'Init failed: $error';
  }

  @override
  String leader_global_load_failed(String error) {
    return 'Failed to load global rankings: $error';
  }

  @override
  String leader_local_load_failed(String error) {
    return 'Failed to load country rankings (personal): $error';
  }

  @override
  String leader_country_load_failed(String error) {
    return 'Failed to load country rankings: $error';
  }

  @override
  String get leader_prompt_set_country => 'Please set your country in Profile first.';

  @override
  String get leader_back => 'Back';

  @override
  String leader_country_title(String flag, String code) {
    return '$flag $code';
  }

  @override
  String leader_avg_ms(String avg) {
    return 'Avg $avg ms';
  }

  @override
  String leader_participants(String count) {
    return 'Participants $count';
  }

  @override
  String leader_my_country(String code) {
    return 'My country: $code';
  }

  @override
  String leader_my_best_ms(String ms) {
    return 'My best: $ms ms';
  }

  @override
  String leader_my_global_rank(String rank) {
    return 'My global rank: #$rank';
  }

  @override
  String leader_my_country_rank(String rank) {
    return 'My country rank: #$rank';
  }

  @override
  String leader_country_avg_ms(String ms) {
    return 'Country average: $ms ms';
  }

  @override
  String leader_country_median_ms(String ms) {
    return 'Country median: $ms ms';
  }

  @override
  String leader_country_top_ms(String ms) {
    return 'Country best: $ms ms';
  }

  @override
  String leader_country_player_count(String count) {
    return 'Players: $count';
  }

  @override
  String get result_title => 'Result';

  @override
  String get result_tier_base => 'Tier is based on the average of 3 trials';

  @override
  String result_top(num percent) {
    return 'Top $percent%';
  }

  @override
  String result_current_rank(int rank) {
    return 'Current global rank: #$rank';
  }

  @override
  String result_best_ms(int ms) {
    return 'Your best: $ms ms';
  }

  @override
  String get result_retry => 'Retry';

  @override
  String get result_go_home => 'Home';

  @override
  String get result_open_leaderboard => 'Open leaderboard';

  @override
  String get result_saving => 'Saving your records...';

  @override
  String result_try1(String ms) {
    return '1st: $ms';
  }

  @override
  String result_try2(String ms) {
    return '2nd: $ms';
  }

  @override
  String result_try3(String ms) {
    return '3rd: $ms';
  }

  @override
  String result_average_ms(String ms) {
    return 'Average: $ms ms';
  }

  @override
  String get test_title => 'Reaction Speed Test';

  @override
  String get test_sound_on => 'Turn sound off';

  @override
  String get test_sound_off => 'Turn sound on';

  @override
  String get test_haptics_on => 'Turn haptics off';

  @override
  String get test_haptics_off => 'Turn haptics on';

  @override
  String get test_wait => 'Wait…';

  @override
  String get test_tap_now => 'Tap now!';

  @override
  String get test_too_fast => 'Too soon';

  @override
  String get test_too_soon_hint => 'Too soon! Tap after it turns green.';

  @override
  String get test_hint_countdown => 'Starting in 3 seconds';

  @override
  String get test_hint_waiting => 'Tap when the button turns green';

  @override
  String get test_hint_green => 'Now! Tap as fast as you can!';

  @override
  String get test_hint_lockout => 'Too soon — restarting';

  @override
  String get test_try1_short => '1st';

  @override
  String get test_try2_short => '2nd';

  @override
  String get test_try3_short => '3rd';

  @override
  String get profile_title => 'Profile';

  @override
  String get profile_country_label => 'Country';

  @override
  String get profile_country_hint => 'Select your country';

  @override
  String get profile_unset => 'Not set';

  @override
  String get profile_pick => 'Pick';

  @override
  String get profile_display_name_label => 'Display name (shown on leaderboard)';

  @override
  String get profile_display_name_autogen => 'Generating... It will be auto-created on save';

  @override
  String get profile_save => 'Save';

  @override
  String get profile_saved_title => 'Profile';

  @override
  String get profile_saved_message => 'Saved.';

  @override
  String get profile_error_title => 'Error';

  @override
  String profile_save_failed(String error) {
    return 'Save failed: $error';
  }

  @override
  String profile_load_failed(String error) {
    return 'Failed to load profile: $error';
  }

  @override
  String profile_user_id(String id) {
    return 'User ID: $id';
  }

  @override
  String get common_unknown => 'Unknown';

  @override
  String get common_anonymous => 'Anonymous';

  @override
  String get common_error => 'Error';

  @override
  String common_profile_load_failed_fallback(String error) {
    return 'Failed to load profile: $error';
  }

  @override
  String get common_close => 'Close';

  @override
  String get common_notice => 'Notice';

  @override
  String country_code(Object code) {
    return '$code';
  }
}
