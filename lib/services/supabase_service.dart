import 'dart:developer' as dev;
import 'dart:math';

import 'package:supabase_flutter/supabase_flutter.dart';

/// Supabase 서비스 (주간 시즌 제거 / 권장 옵션 A: View security_invoker)
/// - 서버 RPC/뷰 설계에 맞춘 클라이언트 래퍼
/// - 개인/국가 리더보드, 내 스탯, 프로필 업데이트 등
class SupabaseService {
  SupabaseService._();
  static final SupabaseService _instance = SupabaseService._();
  factory SupabaseService() => _instance;

  SupabaseClient get _client => Supabase.instance.client;

  // ---------------------------------------------------------------------------
  // Username generator (익명 닉네임 보장)
  static const List<String> _colors = [
    'red', 'blue', 'green', 'yellow', 'purple', 'orange', 'black', 'white', 'gray', 'silver',
    'gold', 'pink', 'brown', 'cyan', 'magenta', 'navy', 'teal', 'maroon', 'olive', 'violet',
  ];
  static const List<String> _animals = [
    'tiger', 'lion', 'eagle', 'wolf', 'fox', 'bear', 'panda', 'hawk', 'falcon', 'shark',
    'whale', 'otter', 'owl', 'horse', 'panther', 'cobra', 'viper', 'rhino', 'giraffe', 'leopard',
  ];

  String _randomUsername(Random rng) {
    final color = _colors[rng.nextInt(_colors.length)];
    final animal = _animals[rng.nextInt(_animals.length)];
    final num = rng.nextInt(9999) + 1; // 1..9999
    return '$color $animal $num';
  }

  /// 현재 사용자의 username이 없으면 생성해서 저장. (중복 회피 포함)
  Future<String?> ensureUsername() async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) return null;

    final profile = await _client
        .from('profiles')
        .select('username')
        .eq('id', uid)
        .maybeSingle();
    final existing = (profile?['username'] as String?)?.trim();
    if (existing != null && existing.isNotEmpty) return existing;

    final rng = Random.secure();
    for (int attempt = 0; attempt < 20; attempt++) {
      final candidate = _randomUsername(rng);
      final taken = await _isUsernameTaken(candidate);
      if (!taken) {
        try {
          await _client.from('profiles').update({'username': candidate}).eq('id', uid);
          return candidate;
        } on PostgrestException catch (e) {
          if (e.code == '23505') continue; // unique_violation
          rethrow;
        }
      }
    }
    // Fallback 강제 유니크
    final fallback = '${_randomUsername(rng)}-${rng.nextInt(90000) + 10000}';
    try {
      await _client.from('profiles').update({'username': fallback}).eq('id', uid);
      return fallback;
    } on PostgrestException catch (e) {
      if (e.code == '23505') {
        final hard = '$fallback-${DateTime.now().millisecondsSinceEpoch % 1000}';
        await _client.from('profiles').update({'username': hard}).eq('id', uid);
        return hard;
      }
      rethrow;
    }
  }

  Future<bool> _isUsernameTaken(String name) async {
    final row = await _client
        .from('profiles')
        .select('id')
        .eq('username', name)
        .maybeSingle();
    return row != null;
  }

  // ---------------------------------------------------------------------------
  // Scores
  Future<void> recordScore(int ms) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) return;
    await _client.from('scores').insert({'user_id': uid, 'ms': ms});
  }

  // ---------------------------------------------------------------------------
  // Leaderboards & Stats (RPC 중심)

  /// 개인 리더보드 (글로벌 / 특정 국가)
  /// 서버 RPC: rpc_leaderboard_personal(_country_code, _limit, _offset)
  /// 반환: [{rank, user_id, username, country_code, best_ms}, ...]
  Future<List<Map<String, dynamic>>> getLeaderboardPersonal({
    String? country,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final res = await _client.rpc('rpc_leaderboard_personal', params: {
        '_country_code': country,
        '_limit': limit,
        '_offset': offset,
      });
      final list = (res as List<dynamic>).cast<Map<String, dynamic>>();
      return list;
    } on PostgrestException catch (e, st) {
      dev.log('getLeaderboardPersonal PostgrestException: ${e.message}', stackTrace: st);
      rethrow;
    } catch (e, st) {
      dev.log('getLeaderboardPersonal error: $e', stackTrace: st);
      rethrow;
    }
  }

  /// 국가 랭킹 (전체 기준)
  /// 서버 RPC: rpc_leaderboard_countries(_limit, _offset)
  /// 반환: [{country_rank, country_code, avg_ms, participants}, ...]
  Future<List<Map<String, dynamic>>> getLeaderboardCountries({
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final res = await _client.rpc('rpc_leaderboard_countries', params: {
        '_limit': limit,
        '_offset': offset,
      });
      final list = (res as List<dynamic>).cast<Map<String, dynamic>>();
      return list;
    } on PostgrestException catch (e, st) {
      dev.log('getLeaderboardCountries PostgrestException: ${e.message}', stackTrace: st);
      rethrow;
    } catch (e, st) {
      dev.log('getLeaderboardCountries error: $e', stackTrace: st);
      rethrow;
    }
  }

  /// 내 요약 스탯 (내 베스트/글로벌/국가 랭크, 내 국가 평균 등)
  /// 서버 RPC: rpc_my_stats()
  Future<Map<String, dynamic>?> getMyStats() async {
    final result = await _client.rpc('rpc_my_stats');
    final list = (result as List).cast<Map<String, dynamic>>();
    return list.isEmpty ? null : list.first;
  }

  /// (선택) 특정 국가의 요약 통계
  /// 서버에 rpc_country_stats(country_code) 가 있다면 사용, 없으면 null 반환
  Future<Map<String, dynamic>?> getCountryStats(String countryCode) async {
    try {
      final res = await _client.rpc('rpc_country_stats', params: {
        '_country_code': countryCode,
      });
      if (res == null) return null;
      if (res is List && res.isNotEmpty) {
        return (res.first as Map).cast<String, dynamic>();
      }
      if (res is Map) return res.cast<String, dynamic>();
      return null;
    } on PostgrestException catch (e, st) {
      // 함수 미존재/권한 등은 무시하고 null
      dev.log('getCountryStats PostgrestException: ${e.message}', stackTrace: st);
      return null;
    } catch (e, st) {
      dev.log('getCountryStats error: $e', stackTrace: st);
      return null;
    }
  }

  /// (옵션 A) 특정 유저의 글로벌 랭크를 조회하고 싶을 때 — 뷰로 접근
  /// v_user_rank_global 이 security_invoker=on 이면 RLS 하에서 안전하게 조회 가능
  Future<Map<String, dynamic>?> getUserRank(String userId) async {
    try {
      final row = await _client
          .from('v_user_rank_global')
          .select('global_rank, best_ms')
          .eq('user_id', userId)
          .maybeSingle();
      if (row == null) return null;
      return {
        'rank': row['global_rank'],
        'best_ms': row['best_ms'],
      };
    } on PostgrestException catch (e, st) {
      dev.log('getUserRank PostgrestException: ${e.message}', stackTrace: st);
      rethrow;
    } catch (e, st) {
      dev.log('getUserRank error: $e', stackTrace: st);
      rethrow;
    }
  }

  // ---------------------------------------------------------------------------
  // Profile
  Future<void> updateCountry(String countryCode) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) {
      dev.log('updateCountry: No authenticated user');
      return;
    }
    try {
      await _client
          .from('profiles')
          .update({'country_code': countryCode.toUpperCase()})
          .eq('id', uid);
    } on PostgrestException catch (e, st) {
      dev.log('updateCountry PostgrestException: ${e.message}', stackTrace: st);
      rethrow;
    } catch (e, st) {
      dev.log('updateCountry error: $e', stackTrace: st);
      rethrow;
    }
  }

  Future<void> updateUsername(String username) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) return;
    try {
      await _client.from('profiles').update({'username': username}).eq('id', uid);
    } on PostgrestException catch (e, st) {
      dev.log('updateUsername PostgrestException: ${e.message}', stackTrace: st);
      rethrow;
    } catch (e, st) {
      dev.log('updateUsername error: $e', stackTrace: st);
      rethrow;
    }
  }

  Future<Map<String, dynamic>?> getMyProfile() async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) return null;
    try {
      final data = await _client
          .from('profiles')
          .select('id, username, country_code')
          .eq('id', uid)
          .maybeSingle();
      return data;
    } on PostgrestException catch (e, st) {
      dev.log('getMyProfile PostgrestException: ${e.message}', stackTrace: st);
      rethrow;
    } catch (e, st) {
      dev.log('getMyProfile error: $e', stackTrace: st);
      rethrow;
    }
  }

  Future<List<Map<String, dynamic>>> getLeaderboard({String? country, int limit = 50, int offset = 0}) async {
    final res = await _client.rpc('get_leaderboard', params: {
      'p_country': country,
      'p_limit': limit,
      'p_offset': offset,
    });
    return (res as List).cast<Map<String, dynamic>>();
  }


// tier_asset 헬퍼(서버 tier_key가 올 때)
  String? tierAssetFrom(Map<String, dynamic>? stats) {
    final key = stats?['tier_key'] as String?; // e.g., 'gold'
    return key == null ? null : 'assets/tier_$key.png';
  }

  // 추가: 한 방에 제출 + 최신 스탯
  Future<Map<String, dynamic>?> submitBestAndGetStats(int bestMs) async {
    try {
      final res = await _client.rpc('rpc_submit_and_stats', params: {'_ms': bestMs});
      if (res == null) return null;
      if (res is List && res.isNotEmpty) return (res.first as Map).cast<String, dynamic>();
      if (res is Map) return res.cast<String, dynamic>();
      return null;
    } on PostgrestException catch (e, st) {
      dev.log('submitBestAndGetStats PostgrestException: ${e.message}', stackTrace: st);
      rethrow;
    } catch (e, st) {
      dev.log('submitBestAndGetStats error: $e', stackTrace: st);
      rethrow;
    }
  }
}

