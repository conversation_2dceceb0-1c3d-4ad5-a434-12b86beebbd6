import 'dart:math' as math;
import 'dart:ui';
import 'package:flutter/material.dart';

class TierEmblem extends StatefulWidget {
  const TierEmblem({super.key, this.asset, this.tierKey, this.size = 30});

  final String? asset;    // required to render emblem image
  final String? tierKey;  // 'iron' | 'bronze' | ... or display name like 'Gold'
  final double size;

  @override
  State<TierEmblem> createState() => _TierEmblemState();
}

class _TierEmblemState extends State<TierEmblem>
    with TickerProviderStateMixin {
  late final AnimationController _pulse;   // scale/opacity pulse
  late final AnimationController _rotate;  // sweep gradient rotation

  @override
  void initState() {
    super.initState();
    _pulse = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2400),
    )..repeat();

    _rotate = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 6000),
    )..repeat();
  }

  @override
  void dispose() {
    _pulse.dispose();
    _rotate.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = _TierTheme.from(widget.tierKey);
    final cs = Theme.of(context).colorScheme;

    final double sz = widget.size;
    final double glowRadius = lerpDouble(8, 34, theme.intensity)!;   // blur strength
    final double haloScale = lerpDouble(1.08, 1.28, theme.intensity)!; // outer halo size
    final double pulseAmt = lerpDouble(0.02, 0.10, theme.intensity)!; // scale pulsing
    final double bgOpacity = lerpDouble(0.20, 0.35, theme.intensity)!; // aura opacity

    final baseColor = theme.primary;
    final secColor  = theme.secondary ?? baseColor;

    return SizedBox(
      width: sz,
      height: sz,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 1) Soft radial halo (pulsing scale)
          AnimatedBuilder(
            animation: _pulse,
            builder: (_, __) {
              final t = _pulse.value;                            // 0..1
              final wave = (math.sin(t * math.pi * 2) + 1) / 2;  // 0..1
              final scale = 1 + pulseAmt * wave;                 // light pulsing
              return Transform.scale(
                scale: haloScale * scale,
                child: _blurredCircle(
                  colors: [
                    baseColor.withOpacity(bgOpacity),
                    baseColor.withOpacity(bgOpacity * 0.25),
                    Colors.transparent,
                  ],
                  stops: const [0.0, 0.55, 1.0],
                  blur: glowRadius,
                ),
              );
            },
          ),

          // 2) Rotating sweep highlights (subtle sparkles)
          AnimatedBuilder(
            animation: _rotate,
            builder: (_, __) {
              final angle = _rotate.value * math.pi * 2;
              return Transform.rotate(
                angle: angle,
                child: _sweepRing(
                  color1: secColor.withOpacity(0.35 + 0.25 * theme.intensity),
                  color2: baseColor.withOpacity(0.10 + 0.20 * theme.intensity),
                ),
              );
            },
          ),

          // 3) Inner glass plate (very subtle)
          ClipOval(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
              child: Container(
                width: sz * 0.94,
                height: sz * 0.94,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: cs.surface.withOpacity(0.07 * theme.intensity),
                ),
              ),
            ),
          ),

          // 4) Emblem asset with sharpness + drop shadow
          if (widget.asset != null && widget.asset!.isNotEmpty)
            Container(
              width: sz,
              height: sz,
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: baseColor.withOpacity(0.35 + 0.20 * theme.intensity),
                    blurRadius: glowRadius,
                    spreadRadius: lerpDouble(0, 2, theme.intensity)!,
                  ),
                ],
              ),
              child: Image.asset(widget.asset!, fit: BoxFit.contain),
            )
          else
          // skeleton fallback
            ClipOval(child: Container(color: cs.surfaceVariant)),
        ],
      ),
    );
  }

  Widget _blurredCircle({required List<Color> colors, required List<double> stops, double blur = 18}) {
    return ClipOval(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
        child: DecoratedBox(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(colors: colors, stops: stops),
          ),
          child: const SizedBox.expand(),
        ),
      ),
    );
  }

  Widget _sweepRing({required Color color1, required Color color2}) {
    return ClipOval(
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
        child: DecoratedBox(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: SweepGradient(
              colors: [
                Colors.transparent,
                color1,
                Colors.transparent,
                color2,
                Colors.transparent,
              ],
              stops: const [0.0, 0.15, 0.35, 0.55, 1.0],
            ),
          ),
          child: const SizedBox.expand(),
        ),
      ),
    );
  }
}

class _TierTheme {
  _TierTheme(this.primary, {this.secondary, required this.intensity});
  final Color primary;       // main aura color
  final Color? secondary;    // highlight color
  final double intensity;    // 0.0 (iron) .. 1.0 (challenger)

  static _TierTheme from(String? key) {
    final k = (key ?? '').toLowerCase();
    // Accept display names or asset-style keys
    switch (k) {
      case 'iron':
        return _TierTheme(Colors.blueGrey.shade600, intensity: 0.05);
      case 'bronze':
        return _TierTheme(const Color(0xFF8C5A2B), secondary: Colors.deepOrange.shade300, intensity: 0.15);
      case 'silver':
        return _TierTheme(const Color(0xFF9FA6B2), secondary: Colors.white70, intensity: 0.25);
      case 'gold':
        return _TierTheme(const Color(0xFFFFC107), secondary: const Color(0xFFFFE082), intensity: 0.38);
      case 'platinum':
        return _TierTheme(const Color(0xFF00B8D4), secondary: const Color(0xFF64FFDA), intensity: 0.52);
      case 'diamond':
        return _TierTheme(const Color(0xFF448AFF), secondary: const Color(0xFF82B1FF), intensity: 0.68);
      case 'master':
        return _TierTheme(const Color(0xFFAB47BC), secondary: const Color(0xFFE1BEE7), intensity: 0.80);
      case 'grandmaster':
        return _TierTheme(const Color(0xFFE53935), secondary: const Color(0xFFFF8A80), intensity: 0.92);
      case 'challenger':
        return _TierTheme(const Color(0xFF536DFE), secondary: const Color(0xFF40C4FF), intensity: 1.00);
      default:
      // try to infer from filename like assets/tier_gold.png
        if (k.contains('challenger')) return _TierTheme(const Color(0xFF536DFE), secondary: const Color(0xFF40C4FF), intensity: 1.00);
        if (k.contains('grandmaster')) return _TierTheme(const Color(0xFFE53935), secondary: const Color(0xFFFF8A80), intensity: 0.92);
        if (k.contains('master')) return _TierTheme(const Color(0xFFAB47BC), secondary: const Color(0xFFE1BEE7), intensity: 0.80);
        if (k.contains('diamond')) return _TierTheme(const Color(0xFF448AFF), secondary: const Color(0xFF82B1FF), intensity: 0.68);
        if (k.contains('platinum')) return _TierTheme(const Color(0xFF00B8D4), secondary: const Color(0xFF64FFDA), intensity: 0.52);
        if (k.contains('gold')) return _TierTheme(const Color(0xFFFFC107), secondary: const Color(0xFFFFE082), intensity: 0.38);
        if (k.contains('silver')) return _TierTheme(const Color(0xFF9FA6B2), secondary: Colors.white70, intensity: 0.25);
        if (k.contains('bronze')) return _TierTheme(const Color(0xFF8C5A2B), secondary: Colors.deepOrange.shade300, intensity: 0.15);
        return _TierTheme(Colors.blueGrey.shade600, intensity: 0.05);
    }
  }
}
