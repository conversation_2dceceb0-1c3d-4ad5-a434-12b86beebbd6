// ---------------------------------------------------------------------------
// Sound manager (preloads short SFX and plays with low latency)
// ---------------------------------------------------------------------------
import 'package:just_audio/just_audio.dart';

class SoundFx {
  bool enabled = true;

  final _beep = AudioPlayer(); // countdown
  final _go = AudioPlayer(); // green cue
  final _error = AudioPlayer(); // false start
  final _tap = AudioPlayer(); // successful tap
  final _success = AudioPlayer(); // end of run

  Future<void> init() async {
    try {
      await Future.wait([
        _beep.setAsset('assets/sounds/count_beep.wav'),
        _go.setAsset('assets/sounds/go.wav'),
        _error.setAsset('assets/sounds/error.wav'),
        _tap.setAsset('assets/sounds/tap.wav'),
        _success.setAsset('assets/sounds/success.wav'),
      ]);
// Reduce latency (small buffer)
      await _beep.setVolume(0.9);
      await _go.setVolume(1.0);
      await _error.setVolume(0.9);
      await _tap.setVolume(0.7);
      await _success.setVolume(0.9);
    } catch (_) {
// ignore load errors in dev
    }
  }

  Future<void> dispose() async {
    await _beep.dispose();
    await _go.dispose();
    await _error.dispose();
    await _tap.dispose();
    await _success.dispose();
  }

  void beep() { if (enabled) _beep.seek(Duration.zero).then((_) => _beep.play()); }
  void go() { if (enabled) _go.seek(Duration.zero).then((_) => _go.play()); }
  void error() { if (enabled) _error.seek(Duration.zero).then((_) => _error.play()); }
  void tap() { if (enabled) _tap.seek(Duration.zero).then((_) => _tap.play()); }
  void success() { if (enabled) _success.seek(Duration.zero).then((_) => _success.play()); }
}