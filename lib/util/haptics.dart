import 'package:flutter/services.dart';

class Haptics {
  static bool enabled = true;

  static Future<void> _safeCall(Future<void> Function() f) async {
    if (!enabled) return;
    try {
      await f();
    } catch (_) {
      /* no-op */
    }
  }

  /// Light tap feedback (button press)
  static Future<void> light() async => _safeCall(() async {
        await HapticFeedback.lightImpact();
// Fallback (commented out by default)
// if (!kIsWeb && !(await Vibration.hasAmplitudeControl() ?? false)) {
//   if (await Vibration.hasVibrator() ?? false) Vibration.vibrate(duration: 15);
// }
      });

  /// Medium emphasis (success)
  static Future<void> medium() async => _safeCall(() async {
        await HapticFeedback.mediumImpact();
// if (!kIsWeb && (await Vibration.hasCustomVibrationsSupport() ?? false)) {
//   Vibration.vibrate(duration: 22, amplitude: 180);
// }
      });

  /// Heavy emphasis (GO cue, false start)
  static Future<void> heavy() async => _safeCall(() async {
        await HapticFeedback.heavyImpact();
// if (!kIsWeb && (await Vibration.hasCustomVibrationsSupport() ?? false)) {
//   Vibration.vibrate(pattern: [0, 25]);
// }
      });

  /// Subtle tick (countdown)
  static Future<void> tick() async => _safeCall(() async {
        await HapticFeedback.selectionClick();
      });
}
