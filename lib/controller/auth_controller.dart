import 'package:flutter_reaction_speed/services/supabase_service.dart';
import 'package:get/get.dart';
import 'package:flutter_reaction_speed/main.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AuthController extends GetxController {
  // SupabaseService 인스턴스를 Get을 통해 주입받음
  final SupabaseService _supabaseService = Get.find();

  // Rx 변수들을 사용하여 상태 변화를 감지하고 UI를 자동으로 업데이트
  final RxBool isLoading = true.obs;
  final RxBool hasCountry = false.obs;
  final RxString username = ''.obs;
  final Rx<User?> user = Rx<User?>(null);

  @override
  void onInit() {
    super.onInit();
    // 컨트롤러 초기화 시 Supabase 클라이언트의 유저 정보를 가져옴
    user.value = Supabase.instance.client.auth.currentUser;
    // 프로필 상태를 확인하는 비동기 함수 호출
    _checkUserProfile();
  }

  // 사용자 프로필 정보를 확인하고 상태를 업데이트하는 함수
  Future<void> _checkUserProfile() async {
    try {
      isLoading.value = true;
      // 익명 로그인이 되어있지 않다면 시도
      if (user.value == null) {
        await Supabase.instance.client.auth.signInAnonymously();
        user.value = Supabase.instance.client.auth.currentUser;
      }

      // 사용자 이름이 없다면 생성/확보
      await _supabaseService.ensureUsername();

      // 내 프로필 정보 가져오기
      final profile = await _supabaseService.getMyProfile();

      // 프로필 정보에 따라 상태 변수 업데이트
      if (profile != null) {
        username.value = (profile['username'] as String?)?.trim() ?? '';
        hasCountry.value = (profile['country_code'] as String?)?.isNotEmpty == true;
      }
    } catch (e) {
      final ctx = Get.context;
      if (ctx != null) {
        Get.snackbar(ctx.l10n.profile_error_title, ctx.l10n.profile_load_failed('$e'));
      } else {
        Get.snackbar('Error', 'Failed to load profile: $e');
      }
      // 오류 발생 시 기본값으로 설정
      username.value = '';
      hasCountry.value = false;
    } finally {
      // 작업 완료 후 로딩 상태 해제
      isLoading.value = false;
    }
  }
}
