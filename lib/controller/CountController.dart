// lib/controller/count_controller.dart
import 'package:get/get.dart';

/// 티어 정의 (이름/에셋 일관 관리)
enum Tier { iron, bronze, silver, gold, platinum, diamond, master, grandmaster, challenger }

extension TierX on Tier {
  String get label {
    switch (this) {
      case Tier.iron: return 'Iron';
      case Tier.bronze: return 'Bronze';
      case Tier.silver: return 'Silver';
      case Tier.gold: return 'Gold';
      case Tier.platinum: return 'Platinum';
      case Tier.diamond: return 'Diamond';
      case Tier.master: return 'Master';
      case Tier.grandmaster: return 'Grandmaster';
      case Tier.challenger: return 'Challenger';
    }
  }

  String get asset {
    switch (this) {
      case Tier.iron:        return 'assets/tier_iron.png';
      case Tier.bronze:      return 'assets/tier_bronze.png';
      case Tier.silver:      return 'assets/tier_silver.png';
      case Tier.gold:        return 'assets/tier_gold.png';
      case Tier.platinum:    return 'assets/tier_platinum.png';
      case Tier.diamond:     return 'assets/tier_diamond.png';
      case Tier.master:      return 'assets/tier_master.png';
      case Tier.grandmaster: return 'assets/tier_grandmaster.png';
      case Tier.challenger:  return 'assets/tier_challenger.png';
    }
  }
}

class CountController extends GetxController {
  // ----- Session / attempts ---------------------------------------------------
  static const int maxAttempts = 3;

  final RxInt _attempt = 0.obs;          // 현재까지 시도 횟수(0..3)
  int get attempt => _attempt.value;
  int get attemptsLeft => (maxAttempts - _attempt.value).clamp(0, maxAttempts);

  final Rx<DateTime> _startedAt = DateTime.now().obs;
  DateTime get startedAt => _startedAt.value;
  void markStart([DateTime? t]) => _startedAt.value = t ?? DateTime.now();

  // 각 기록(ms) — null이면 아직 미기록
  final RxnInt firstRecord  = RxnInt();
  final RxnInt secondRecord = RxnInt();
  final RxnInt thirdRecord  = RxnInt();

  // 파생 상태
  final RxInt averageMs = 0.obs;        // 현재 입력된 기록들의 평균(ms)
  final RxDouble percentile = 100.0.obs; // 낮을수록 상위(예: 0.73 == 상위 0.73%)
  final Rx<Tier> tier = Tier.iron.obs;

  // ----- 퍼센타일 룰(로컬 fallback) ------------------------------------------
  // ms <= upperBound -> percentile
  static const List<(int upper, double pct)> _percentileBreaks = [
    (110, 0.94), (120, 2.12), (130, 3.62), (140, 5.92), (150, 9.47),
    (160, 14.80), (190, 22.05), (250, 30.70), (270, 39.80), (290, 49.00),
    (310, 58.00), (330, 67.00), (350, 77.87), (370, 84.25), (390, 88.80),
    (410, 91.90), (450, 96.52),
  ];

  static double _percentileForMs(int ms) {
    for (final (upper, pct) in _percentileBreaks) {
      if (ms <= upper) return pct;
    }
    return 100.0;
  }

  // 퍼센타일 기반 티어(추천) — 상위% 기준
  static Tier tierForPercentile(double p) {
    if (p <= 0.1)  return Tier.challenger;
    if (p <= 1.0)  return Tier.grandmaster;
    if (p <= 3.0)  return Tier.master;
    if (p <= 10.0) return Tier.diamond;
    if (p <= 20.0) return Tier.platinum;
    if (p <= 40.0) return Tier.gold;
    if (p <= 70.0) return Tier.silver;
    if (p <= 95.0) return Tier.bronze;
    return Tier.iron;
  }

  // ms 기반 티어(fallback — 기존 룰 유지)
  static Tier tierForMs(int ms) {
    if (ms <= 45)  return Tier.challenger;
    if (ms <= 60)  return Tier.grandmaster;
    if (ms <= 100) return Tier.master;
    if (ms <= 130) return Tier.diamond;
    if (ms <= 160) return Tier.platinum;
    if (ms <= 270) return Tier.gold;
    if (ms <= 330) return Tier.silver;
    if (ms <= 450) return Tier.bronze;
    return Tier.iron;
  }

  // ----- Public APIs ----------------------------------------------------------

  /// 시도 기록을 1회 추가한다. (ms 단위)
  /// 반환: 3회 모두 채워졌으면 true
  bool recordAttempt(int ms) {
    if (_attempt.value >= maxAttempts) return false;
    _attempt.value++;
    switch (_attempt.value) {
      case 1: firstRecord.value  = ms; break;
      case 2: secondRecord.value = ms; break;
      case 3: thirdRecord.value  = ms; break;
    }
    _recompute();
    return _attempt.value >= maxAttempts;
  }

  /// 평균/퍼센타일/티어 재계산 (로컬 규칙)
  void _recompute() {
    final vals = <int>[];
    if (firstRecord.value  != null) vals.add(firstRecord.value!);
    if (secondRecord.value != null) vals.add(secondRecord.value!);
    if (thirdRecord.value  != null) vals.add(thirdRecord.value!);

    if (vals.isEmpty) {
      averageMs.value = 0;
      percentile.value = 100.0;
      tier.value = Tier.iron;
      return;
    }

    averageMs.value = (vals.reduce((a, b) => a + b) / vals.length).round();

    // 퍼센타일 기준 티어를 기본으로 사용
    final p = _percentileForMs(averageMs.value);
    percentile.value = p;
    tier.value = tierForPercentile(p);
  }

  /// 서버에서 내려준 퍼센타일/티어가 있으면 여기로 덮어쓰기
  /// (예: rpc_my_stats_v2의 global_percentile + tier)
  void applyServerTier({double? percentileGlobal, String? tierName}) {
    if (percentileGlobal != null) {
      percentile.value = percentileGlobal;
      tier.value = tierForPercentile(percentileGlobal);
    }
    if (tierName != null) {
      final normalized = tierName.toLowerCase();
      final mapped = {
        'iron': Tier.iron,
        'bronze': Tier.bronze,
        'silver': Tier.silver,
        'gold': Tier.gold,
        'platinum': Tier.platinum,
        'diamond': Tier.diamond,
        'master': Tier.master,
        'grandmaster': Tier.grandmaster,
        'challenger': Tier.challenger,
      }[normalized];
      if (mapped != null) tier.value = mapped;
    }
  }

  /// 세션 리셋 (다음 게임)
  void resetSession() {
    _attempt.value = 0;
    firstRecord.value  = null;
    secondRecord.value = null;
    thirdRecord.value  = null;
    averageMs.value = 0;
    percentile.value = 100.0;
    tier.value = Tier.iron;
    markStart();
  }

  // ---- 호환 API (기존 코드 대체용) ------------------------------------------
  void updateElapsedTime(DateTime time) => _startedAt.value = time;
  DateTime getElapsedTime() => _startedAt.value;

  void resetRecords() => resetSession();

  void setAverageRecord(int average) {
    // 외부에서 강제 설정 시에도 파생 값 업데이트
    averageMs.value = average;
    percentile.value = _percentileForMs(average);
    tier.value = tierForPercentile(percentile.value);
  }

  /// 기존 getPercent 대체: 문자열로 퍼센타일(소수 2자리)
  String get percentLabel => percentile.value.toStringAsFixed(2);

  /// 기존 getTierImageResource 대체: 현재 티어의 에셋
  String get currentTierAsset => tier.value.asset;

  String get currentTierName => tier.value.label;
}
