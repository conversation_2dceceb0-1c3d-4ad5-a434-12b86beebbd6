import 'package:flutter_reaction_speed/controller/auth_controller.dart';
import 'package:flutter_reaction_speed/services/supabase_service.dart';
import 'package:get/get.dart';

// 앱 전역에서 사용될 컨트롤러와 서비스의 의존성을 주입합니다.
class InitialBinding extends Bindings {
  @override
  List<Bind> dependencies() {
    return [
      // Bind.lazyPut을 사용하여 의존성을 리스트 형태로 반환합니다.
      Bind.lazyPut<SupabaseService>(() => SupabaseService(), fenix: true),
      Bind.lazyPut<AuthController>(() => AuthController(), fenix: true),
    ];
  }
}
