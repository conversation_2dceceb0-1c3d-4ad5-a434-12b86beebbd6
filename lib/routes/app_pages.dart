import 'package:flutter_reaction_speed/screens/home_screen.dart';
import 'package:flutter_reaction_speed/screens/leaderboard_screen.dart';
import 'package:flutter_reaction_speed/screens/profile_screen.dart';
import 'package:flutter_reaction_speed/screens/result_screen.dart';
import 'package:flutter_reaction_speed/screens/speed_test_screen.dart';
import 'package:get/get.dart';

// 라우트 이름을 상수로 관리하여 오타를 방지하고 가독성을 높입니다.
abstract class Routes {
  static const HOME = '/home';
  static const PROFILE = '/profile';
  static const LEADERBOARD = '/leaderboard';
  static const REACTION_TEST = '/reaction-test';
  static const RESULT = '/result';
}

class AppPages {
  static final routes = [
    GetPage(name: Routes.HOME, page: () => const HomeScreen()),
    GetPage(name: Routes.PROFILE, page: () => const ProfileScreen()),
    GetPage(name: Routes.LEADERBOARD, page: () => const LeaderboardScreen()),
    GetPage(name: Routes.REACTION_TEST, page: () => const SpeedTestScreen()),
    GetPage(name: Routes.RESULT, page: () => const ResultScreen()),
  ];
}
