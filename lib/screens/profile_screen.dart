import 'package:country_picker/country_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_reaction_speed/main.dart';
import 'package:flutter_reaction_speed/services/supabase_service.dart';
import 'package:get/get.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  String? _countryCode;
  bool _saving = false;
  Map<String, dynamic>? _profile;

  @override
  void initState() {
    super.initState();
    _loadProfile();
  }

  Future<void> _loadProfile() async {
    final profile = await SupabaseService().getMyProfile();
    if (!mounted) return;
    setState(() {
      _profile = profile;
      _countryCode = profile?['country_code'];
    });
  }

  void _pickCountry() {
    showCountryPicker(
      context: context,
      favorite: ['KR', 'US', 'JP', 'CN'],
      showPhoneCode: false,
      onSelect: (Country c) {
        setState(() => _countryCode = c.countryCode);
      },
    );
  }

  Future<void> _save() async {
    if (_countryCode == null) return;
    if (mounted) setState(() => _saving = true);
    try {
      // Ensure a username exists (auto-generate if missing)
      await SupabaseService().ensureUsername();
      await SupabaseService().updateCountry(_countryCode!);

      // Use Get for non-context-bound notifications/navigation to avoid context of a disposed widget
      Get.snackbar(context.l10n.profile_saved_title, context.l10n.profile_saved_message, duration: const Duration(seconds: 1));
      // Small delay to let the snackbar render before navigation (optional)
      await Future.delayed(const Duration(milliseconds: 200));
      Get.offAllNamed('/home');
    } catch (e) {
      // If still mounted, show error via Get snackbar (does not rely on this widget context)
      Get.snackbar(context.l10n.profile_error_title, context.l10n.profile_save_failed('$e'));
    } finally {
      if (mounted) setState(() => _saving = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(context.l10n.profile_title)),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(context.l10n.profile_country_label, style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: InputDecorator(
                    decoration: InputDecoration(
                      border: OutlineInputBorder(),
                      hintText: context.l10n.profile_country_hint,
                    ),
                    child: Text(_countryCode ?? context.l10n.profile_unset),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _pickCountry,
                  child: Text(context.l10n.profile_pick),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(context.l10n.profile_display_name_label,
                style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            InputDecorator(
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
              ),
              child: Text(
                (_profile?['username'] as String?)?.isNotEmpty == true
                    ? _profile!['username'] as String
                    : context.l10n.profile_display_name_autogen,
              ),
            ),
            const SizedBox(height: 16),

            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: FilledButton(
                onPressed: _saving || _countryCode == null ? null : _save,
                child: _saving
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Text(context.l10n.profile_save),
              ),
            ),
            const SizedBox(height: 24),
            if (_profile != null) ...[
              Text(context.l10n.profile_user_id('${_profile!['id'] ?? ''}')),
            ],
          ],
        ),
      ),
    );
  }

}
