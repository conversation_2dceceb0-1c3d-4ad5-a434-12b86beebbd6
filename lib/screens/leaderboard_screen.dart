import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flutter_reaction_speed/main.dart';
import 'package:flutter_reaction_speed/services/supabase_service.dart';
import 'package:flutter_reaction_speed/widgets/skeletons.dart';

/// 리더보드 화면 (주간 제거 / RPC 기반)
/// ✅ TabBar에 TabController 명시하여 "No TabController for TabBar" 해결
///  - AppBar.bottom 의 TabBar 와 본문 TabBarView 둘 다 같은 _tabController 사용
class LeaderboardScreen extends StatefulWidget {
  const LeaderboardScreen({super.key});

  @override
  State<LeaderboardScreen> createState() => _LeaderboardScreenState();
}

class _LeaderboardScreenState extends State<LeaderboardScreen>
    with SingleTickerProviderStateMixin {
  late final TabController _tabController;

  // 내 프로필/국가/스탯
  String? _myCountry; // e.g., 'KR'
  Map<String, dynamic>? _myStats; // rpc_my_stats()
  Map<String, dynamic>? _myCountryStats; // rpc_country_stats('KR') (선택)

  // 개인(글로벌)
  final List<Map<String, dynamic>> _globalRows = [];
  int _globalOffset = 0;
  bool _globalLoading = false;
  bool _globalEnd = false;

  // 개인(내 국가)
  final List<Map<String, dynamic>> _localRows = [];
  int _localOffset = 0;
  bool _localLoading = false;
  bool _localEnd = false;

  // 국가 랭킹
  final List<Map<String, dynamic>> _countryRows = [];
  int _countryOffset = 0;
  bool _countryLoading = false;
  bool _countryEnd = false;

  final _fmt = NumberFormat.decimalPattern();

  // Derived states for initial loading
  bool get _isGlobalInitial => _globalLoading && _globalRows.isEmpty;
  bool get _isLocalInitial => _localLoading && _localRows.isEmpty;
  bool get _isCountriesInitial => _countryLoading && _countryRows.isEmpty;

  int _adaptiveCount({required BuildContext context, required SkeletonType type}) {
    final size = MediaQuery.of(context).size;
    final tileHeight = type == SkeletonType.personal
        ? LeaderboardSkeletonList.personalTileHeight
        : LeaderboardSkeletonList.countryTileHeight;
    final byHeight = (size.height / tileHeight).floor();
    final count = byHeight.clamp(1, 8);
    return count;
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _init();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _init() async {
    try {
      final profile = await SupabaseService().getMyProfile();
      final myCountry = (profile?['country_code'] as String?)?.toUpperCase();
      final me = await SupabaseService().getMyStats();
      setState(() {
        _myCountry = myCountry;
        _myStats = me;
      });
      await _refreshGlobal();
      await _refreshCountries();
      if (myCountry != null) {
        await _refreshLocal();
        try {
          _myCountryStats = await SupabaseService().getCountryStats(myCountry);
          if (mounted) setState(() {});
        } catch (_) {}
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('초기화 실패: $e')));
    }
  }

  // ----------------------------- 개인(글로벌) ------------------------------
  Future<void> _refreshGlobal() async {
    setState(() {
      _globalRows.clear();
      _globalOffset = 0;
      _globalEnd = false;
    });
    await _loadMoreGlobal();
  }

  Future<void> _loadMoreGlobal() async {
    if (_globalLoading || _globalEnd) return;
    setState(() => _globalLoading = true);
    const pageSize = 50;
    try {
      final rows = await SupabaseService().getLeaderboardPersonal(
        limit: pageSize,
        offset: _globalOffset,
      );
      if (!mounted) return;
      setState(() {
        _globalRows.addAll(rows);
        _globalOffset += rows.length;
        _globalEnd = rows.length < pageSize;
      });
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('글로벌 랭킹 불러오기 실패: $e')));
    } finally {
      if (mounted) setState(() => _globalLoading = false);
    }
  }

  // ----------------------------- 개인(내 국가) ----------------------------
  Future<void> _refreshLocal() async {
    setState(() {
      _localRows.clear();
      _localOffset = 0;
      _localEnd = false;
    });
    await _loadMoreLocal();
  }

  Future<void> _loadMoreLocal() async {
    if (_localLoading || _localEnd || _myCountry == null) return;
    setState(() => _localLoading = true);
    const pageSize = 50;
    try {
      final rows = await SupabaseService().getLeaderboardPersonal(
        country: _myCountry,
        limit: pageSize,
        offset: _localOffset,
      );
      if (!mounted) return;
      setState(() {
        _localRows.addAll(rows);
        _localOffset += rows.length;
        _localEnd = rows.length < pageSize;
      });
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('국가 랭킹(개인) 불러오기 실패: $e')));
    } finally {
      if (mounted) setState(() => _localLoading = false);
    }
  }

  // ----------------------------- 국가 랭킹 --------------------------------
  Future<void> _refreshCountries() async {
    setState(() {
      _countryRows.clear();
      _countryOffset = 0;
      _countryEnd = false;
    });
    await _loadMoreCountries();
  }

  Future<void> _loadMoreCountries() async {
    if (_countryLoading || _countryEnd) return;
    setState(() => _countryLoading = true);
    const pageSize = 50;
    try {
      final rows = await SupabaseService().getLeaderboardCountries(
        limit: pageSize,
        offset: _countryOffset,
      );
      if (!mounted) return;
      setState(() {
        _countryRows.addAll(rows);
        _countryOffset += rows.length;
        _countryEnd = rows.length < pageSize;
      });
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('국가 랭킹 불러오기 실패: $e')));
    } finally {
      if (mounted) setState(() => _countryLoading = false);
    }
  }

  // ----------------------------- UI helpers -------------------------------
  String _flagFromCountry(String? code) {
    if (code == null || code.length != 2) return '🏳️';
    final base = 0x1F1E6;
    final up = code.toUpperCase();
    final a = up.codeUnitAt(0) - 0x41;
    final b = up.codeUnitAt(1) - 0x41;
    return String.fromCharCode(base + a) + String.fromCharCode(base + b);
  }

  Widget _buildPersonalRow(Map<String, dynamic> row) {
    final rank = row['rank'] as int?;
    final username = (row['username'] as String?)?.trim();
    final display = (username == null || username.isEmpty) ? context.l10n.common_unknown : username;
    final code = (row['country_code'] as String?)?.toUpperCase();
    final bestMs = row['best_ms'] as int?;

    IconData? leadIcon;
    Color? iconColor;
    if (rank == 1) { leadIcon = Icons.emoji_events; iconColor = Colors.amber; }
    if (rank == 2) { leadIcon = Icons.emoji_events_outlined; iconColor = Colors.grey; }
    if (rank == 3) { leadIcon = Icons.military_tech_outlined; iconColor = Colors.brown; }

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
        child: leadIcon == null
            ? Text(rank?.toString() ?? '-')
            : Icon(leadIcon, color: iconColor),
      ),
      title: Text(
        display,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Text(
        '${_flagFromCountry(code)} ${code ?? '—'}',
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: Text(bestMs != null ? '${_fmt.format(bestMs)} ms' : '-'),
    );
  }

  Widget _buildCountryRow(Map<String, dynamic> row) {
    final rank = row['country_rank'] as int?;
    final code = (row['country_code'] as String?)?.toUpperCase();
    final avg = row['avg_ms'] as int?;
    final participants = row['participants'] as int?;

    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
        child: Text(rank?.toString() ?? '-'),
      ),
      title: Text('${_flagFromCountry(code)} ${code ?? '—'}'),
      subtitle: Text('평균 ${avg ?? '-'}ms · 참여 ${participants ?? 0}명'),
    );
  }

  Widget _buildLoadMore({
    required bool end,
    required bool loading,
    required VoidCallback loadMore,
    required SkeletonType type,
  }) {
    if (end) {
      // 도달: 더 이상 아이템 없음. 깔끔하게 공간 제거.
      return const SizedBox.shrink();
    }
    if (loading) {
      // 페이지네이션 로딩 중: 스켈레톤 3개 노출
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: LeaderboardSkeletonList(type: type, count: 3),
      );
    }
    // 다음 프레임에 한 번만 로드 트리거 (loading 플래그로 중복 방지)
    WidgetsBinding.instance.addPostFrameCallback((_) => loadMore());
    return const SizedBox.shrink();
  }

  // ----------------------------- BUILD ------------------------------------
  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(context.l10n.leader_title),
        bottom: TabBar(
          controller: _tabController, // ✅ 명시
          tabs: const [
            Tab(text: '개인 · 글로벌'),
            Tab(text: '개인 · 내 국가'),
            Tab(text: '국가 랭킹'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController, // ✅ 명시
        children: [
          // 1) 개인 · 글로벌
          RefreshIndicator(
            onRefresh: _refreshGlobal,
            child: _isGlobalInitial
                ? ListView(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: LeaderboardSkeletonList(
                          type: SkeletonType.personal,
                          count: _adaptiveCount(
                            context: context,
                            type: SkeletonType.personal,
                          ),
                        ),
                      ),
                    ],
                  )
                : ListView.builder(
                    itemCount: _globalRows.length + 1,
                    itemBuilder: (context, i) {
                      if (i < _globalRows.length) return _buildPersonalRow(_globalRows[i]);
                      return _buildLoadMore(
                        end: _globalEnd,
                        loading: _globalLoading,
                        loadMore: _loadMoreGlobal,
                        type: SkeletonType.personal,
                      );
                    },
                  ),
          ),

          // 2) 개인 · 내 국가
          if (_myCountry == null)
            Center(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(context.l10n.leader_prompt_set_country),
                    const SizedBox(height: 12),
                    FilledButton(onPressed: () => Navigator.of(context).pop(), child: Text(context.l10n.leader_back)),
                  ],
                ),
              ),
            )
          else
            Column(
              children: [
                if (_myStats != null || _myCountryStats != null)
                  Padding(
                    padding: const EdgeInsets.fromLTRB(12, 12, 12, 0),
                    child: Card(
                      color: cs.surface,
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('내 국가: ${_myCountry ?? '-'}'),
                            if (_myStats != null) ...[
                              const SizedBox(height: 4),
                              Text('내 베스트: ${_myStats!['my_best_ms'] ?? '-'}ms'),
                              Text('내 글로벌 랭크: #${_myStats!['my_global_rank'] ?? '-'}'),
                              Text('내 국가 랭크: #${_myStats!['my_country_rank'] ?? '-'}'),
                            ],
                            if (_myCountryStats != null) ...[
                              const SizedBox(height: 8),
                              Text('국가 평균: ${_myCountryStats!['avg_best_ms'] ?? '-'}ms'),
                              if (_myCountryStats!.containsKey('median_best_ms'))
                                Text('국가 중앙값: ${_myCountryStats!['median_best_ms']}ms'),
                              if (_myCountryStats!.containsKey('top_best_ms'))
                                Text('국가 최고 기록: ${_myCountryStats!['top_best_ms']}ms'),
                              Text('참여자 수: ${_myCountryStats!['player_count'] ?? 0}명'),
                            ]
                          ],
                        ),
                      ),
                    ),
                  ),
                Expanded(
                  child: RefreshIndicator(
                    onRefresh: _refreshLocal,
                    child: _isLocalInitial
                        ? ListView(
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: 8.0),
                                child: LeaderboardSkeletonList(
                                  type: SkeletonType.personal,
                                  count: _adaptiveCount(
                                    context: context,
                                    type: SkeletonType.personal,
                                  ),
                                ),
                              ),
                            ],
                          )
                        : ListView.builder(
                            itemCount: _localRows.length + 1,
                            itemBuilder: (context, i) {
                              if (i < _localRows.length) return _buildPersonalRow(_localRows[i]);
                              return _buildLoadMore(
                                end: _localEnd,
                                loading: _localLoading,
                                loadMore: _loadMoreLocal,
                                type: SkeletonType.personal,
                              );
                            },
                          ),
                  ),
                ),
              ],
            ),

          // 3) 국가 랭킹
          RefreshIndicator(
            onRefresh: _refreshCountries,
            child: _isCountriesInitial
                ? ListView(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: LeaderboardSkeletonList(
                          type: SkeletonType.country,
                          count: _adaptiveCount(
                            context: context,
                            type: SkeletonType.country,
                          ),
                        ),
                      ),
                    ],
                  )
                : ListView.builder(
                    itemCount: _countryRows.length + 1,
                    itemBuilder: (context, i) {
                      if (i < _countryRows.length) return _buildCountryRow(_countryRows[i]);
                      return _buildLoadMore(
                        end: _countryEnd,
                        loading: _countryLoading,
                        loadMore: _loadMoreCountries,
                        type: SkeletonType.country,
                      );
                    },
                  ),
          ),
        ],
      ),
    );
  }
}
