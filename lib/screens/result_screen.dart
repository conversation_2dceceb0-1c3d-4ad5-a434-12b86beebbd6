import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_reaction_speed/main.dart';
import 'package:flutter_reaction_speed/controller/CountController.dart';
import 'package:flutter_reaction_speed/screens/speed_test_screen.dart';
import 'package:flutter_reaction_speed/services/supabase_service.dart';
import 'package:flutter_reaction_speed/util/tier_emblem.dart';
import 'package:get/get.dart';

class ResultScreen extends StatefulWidget {
  const ResultScreen({super.key});

  @override
  State<ResultScreen> createState() => _ResultScreenState();
}

class _ResultScreenState extends State<ResultScreen> {
  late final CountController c;
  bool _submitted = false;
  bool _submitting = false;

  int? _myRank;
  int? _myBestMs;

  @override
  void initState() {
    super.initState();
    c = Get.find<CountController>();

    // (안전망) 만약 평균이 아직 계산되지 않았다면 한 번 계산
    _ensureAverageComputed();

    // 화면 진입 후 1프레임 뒤 네트워크 작업(중복 업로드 방지)
    WidgetsBinding.instance.addPostFrameCallback((_) => _submitOnce());
  }

  void _ensureAverageComputed() {
    final vals = <int>[];
    if (c.firstRecord.value != null) vals.add(c.firstRecord.value!);
    if (c.secondRecord.value != null) vals.add(c.secondRecord.value!);
    if (c.thirdRecord.value != null) vals.add(c.thirdRecord.value!);
    if (vals.isNotEmpty) {
      final avg = (vals.reduce((a, b) => a + b) / vals.length).round();
      // setAverageRecord는 파생값(퍼센타일/티어)까지 갱신
      c.setAverageRecord(avg);
    }
  }

  Future<void> _submitOnce() async {
    if (_submitted || _submitting) return;
    _submitting = true;

    try {
      final svc = SupabaseService();

      // 세션 베스트(최소 ms)만 추출
      final attempts = [
        c.firstRecord.value,
        c.secondRecord.value,
        c.thirdRecord.value,
      ].whereType<int>().where((ms) => ms > 0).toList();

      if (attempts.isEmpty) {
        _submitted = true;
        _submitting = false;
        return;
      }
      final bestMs = attempts.reduce((a, b) => a < b ? a : b);

      // ✅ 한 번의 RPC로: (1) 더 좋으면 insert, (2) 최신 스탯 반환
      final me = await svc.submitBestAndGetStats(bestMs);
      if (me != null) {
        c.applyServerTier(
          percentileGlobal: (me['global_percentile'] as num?)?.toDouble(),
          tierName: me['tier'] as String?,
        );
        setState(() {
          _myRank   = (me['my_global_rank'] as num?)?.toInt();
          _myBestMs = (me['my_best_ms'] as num?)?.toInt();
        });
      }
    } catch (e) {
      debugPrint('ResultScreen submit error: $e');
    } finally {
      _submitted = true;
      _submitting = false;
    }
  }


  String _msOrDash(int? v) => v == null ? '-' : '${v}ms';

  @override
  Widget build(BuildContext context) {
    final text = Theme.of(context).textTheme;

    return Scaffold(
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
            child: Obx(() {
              final avg = c.averageMs.value;
              final tierAsset = c.currentTierAsset;
              final tierName = c.currentTierName;
              final percent = c.percentLabel;

              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(context.l10n.result_tier_base),
                  const SizedBox(height: 28),

                  // 티어 엠블럼 + 글로우
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      ClipOval(
                        child: BackdropFilter(
                          blendMode: BlendMode.darken,
                          filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
                          child: const SizedBox(width: 160, height: 160),
                        ),
                      ),
                      ClipOval(
                        child: SizedBox(
                          width: 160,
                          height: 160,
                          child: Stack(
                            fit: StackFit.expand,
                            children: [
                              Image.asset(tierAsset, fit: BoxFit.contain),
                              /*TierEmblem(
                                asset: c.currentTierAsset,
                                tierKey: c.currentTierName,
                                size: 160,
                              ),*/
                              Positioned.fill(
                                child: DecoratedBox(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.05),
                                        blurRadius: 24,
                                        spreadRadius: 6,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),
                  Text(context.l10n.result_top(double.tryParse(percent) ?? 0)),

                  if (_myRank != null || _myBestMs != null) ...[
                    const SizedBox(height: 8),
                    if (_myRank != null)
                      Text(context.l10n.result_current_rank(_myRank!), style: text.titleMedium),
                    if (_myBestMs != null)
                      Text(context.l10n.result_best_ms(_myBestMs!), style: text.titleMedium),
                  ],

                  const SizedBox(height: 28),
                  // 개별 기록 표시 (null-safe)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(context.l10n.result_try1(_msOrDash(c.firstRecord.value)), style: text.bodyMedium),
                      const SizedBox(width: 12),
                      Text(context.l10n.result_try2(_msOrDash(c.secondRecord.value)), style: text.bodyMedium),
                      const SizedBox(width: 12),
                      Text(context.l10n.result_try3(_msOrDash(c.thirdRecord.value)), style: text.bodyMedium),
                    ],
                  ),

                  const SizedBox(height: 16),
                  Text(context.l10n.result_average_ms('$avg'), style: text.headlineMedium),

                  const SizedBox(height: 28),
                  FilledButton(
                    onPressed: () {
                      c.resetSession(); // 세션 초기화(시도/평균/티어 리셋)
                      Get.off(() => const SpeedTestScreen());
                    },
                    child: Text(context.l10n.result_retry),
                  ),
                  const SizedBox(height: 12),
                  /*FilledButton.icon(
                    icon: const Icon(Icons.emoji_events),
                    label: const Text('리더보드 보기'),
                    onPressed: () => Get.offAllNamed('/leaderboard'),
                  ),*/
                  const SizedBox(height: 12),
                  FilledButton.icon(
                    icon: const Icon(Icons.home_outlined),
                    label: Text(context.l10n.result_go_home),
                    onPressed: () => Get.offAllNamed('/home'),
                  ),

                  if (_submitting) ...[
                    const SizedBox(height: 20),
                    Text(context.l10n.result_saving, style: text.bodySmall),
                    const SizedBox(height: 6),
                    const LinearProgressIndicator(minHeight: 3),
                  ],
                ],
              );
            }),
          ),
        ),
      ),
    );
  }
}
