import 'dart:async';
import 'dart:math';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_reaction_speed/main.dart';
import 'package:flutter/services.dart';
import 'package:flutter_reaction_speed/controller/CountController.dart';
import 'package:flutter_reaction_speed/util/SoundFx.dart';
import 'package:flutter_reaction_speed/util/haptics.dart';
import 'package:get/get.dart';

import 'package:flutter_reaction_speed/screens/result_screen.dart';
import 'package:flutter_reaction_speed/routes/app_pages.dart';

enum _Phase { countdown, waiting, green, lockout }

// ========================== SpeedTestScreen =================================
class SpeedTestScreen extends StatefulWidget {
  const SpeedTestScreen({super.key});

  @override
  State<SpeedTestScreen> createState() => _SpeedTestScreenState();
}

class _SpeedTestScreenState extends State<SpeedTestScreen>
    with TickerProviderStateMixin {
  late final CountController c;

  _Phase _phase = _Phase.countdown;

  // timing
  Timer? _countdownTimer;
  int _countdown = 3; // 3..0
  int? _greenAtMs; // when green
  int _waitToken = 0; // cancel token

  // randomness bounds
  static const int kMinDelayMs = 1500; // inclusive
  static const int kMaxDelayMs = 4500; // inclusive

  // animations
  late final AnimationController _pressCtrl;
  late final Animation<double> _pressScale;
  late final AnimationController _lockoutCtrl;

  final _sfx = SoundFx();

  @override
  void initState() {
    super.initState();
    c = Get.isRegistered<CountController>()
        ? Get.find<CountController>()
        : Get.put(CountController());
    c.resetSession();

    _pressCtrl = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 140),
      lowerBound: 0.0,
      upperBound: 0.06,
    );
    _pressScale = Tween<double>(begin: 1.0, end: 0.94).animate(
        CurvedAnimation(parent: _pressCtrl, curve: Curves.easeOutCubic));

    _lockoutCtrl = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 520),
    );
    _sfx.init();
    _startRound();
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    _pressCtrl.dispose();
    _lockoutCtrl.dispose();
    super.dispose();
  }

  // ------------------------------- round flow -------------------------------
  void _startRound() {
    _cancelWaiting();
    setState(() {
      _phase = _Phase.countdown;
      _countdown = 3;
      _greenAtMs = null;
    });

    _countdownTimer?.cancel();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (t) async {
      if (!mounted) return;
      if (_countdown <= 0) {
        t.cancel();
        _startWaiting();
      } else {
        _sfx.beep();
        await Haptics.tick();
        setState(() => _countdown -= 1);
      }
    });
  }

  void _startWaiting() {
    setState(() => _phase = _Phase.waiting);
    _greenAtMs = null;

    final token = ++_waitToken;
    final r = Random.secure();
    final delay = kMinDelayMs + r.nextInt(kMaxDelayMs - kMinDelayMs + 1);

    Future.delayed(Duration(milliseconds: delay), () async {
      if (!mounted || token != _waitToken) return;
      setState(() {
        _phase = _Phase.green;
        _greenAtMs = DateTime.now().millisecondsSinceEpoch;
      });
      await Haptics.heavy();
    });
  }

  void _cancelWaiting() {
    _waitToken++;
  }

  void _onTap() async {
    switch (_phase) {
      case _Phase.green:
        final now = DateTime.now().millisecondsSinceEpoch;
        final start = _greenAtMs ?? now;
        final ms = max(0, now - start);
        final finished = c.recordAttempt(ms);
        _pressCtrl.forward().then((_) => _pressCtrl.reverse());
        await Haptics.light();
        if (finished) {
          await Haptics.medium();
          _goResultSafely();
        } else {
          _startRound();
        }
        break;
      case _Phase.waiting:
        _falseStart();
        break;
      case _Phase.countdown:
      case _Phase.lockout:
        break;
    }
  }

  void _falseStart() async {
    _cancelWaiting();
    setState(() => _phase = _Phase.lockout);
    _lockoutCtrl
      ..reset()
      ..forward();
    await Haptics.heavy();
    Future.delayed(const Duration(milliseconds: 700), () {
      if (!mounted) return;
      _startRound();
    });
  }

  // ----------------------- SAFE NAVIGATION (Key fix) ------------------------
  void _goResultSafely() {
    if (!mounted) return;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      try {
        Get.offNamed(Routes.RESULT);
        return;
      } catch (_) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const ResultScreen()),
        );
      }
    });
  }

  // ------------------------------- UI --------------------------------------
  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        title: Text(context.l10n.test_title),
        actions: [
          IconButton(
            tooltip: _sfx.enabled ? context.l10n.test_sound_on : context.l10n.test_sound_off,
            icon: Icon(_sfx.enabled ? Icons.volume_up : Icons.volume_off),
            onPressed: () => setState(() => _sfx.enabled = !_sfx.enabled),
          ),
          IconButton(
            tooltip: Haptics.enabled ? context.l10n.test_haptics_on : context.l10n.test_haptics_off,
            icon: Icon(Haptics.enabled ? Icons.vibration : Icons.view_carousel_outlined),
            onPressed: () => setState(() => Haptics.enabled = !Haptics.enabled),
          ),
          const SizedBox(width: 4),
        ],
      ),
      body: Stack(
        fit: StackFit.expand,
        children: [
          // gradient background
          DecoratedBox(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  cs.primaryContainer.withOpacity(0.35),
                  cs.surfaceVariant.withOpacity(0.3),
                  cs.secondaryContainer.withOpacity(0.35),
                ],
              ),
            ),
          ),
          _GlowingCircle(top: -60, left: -40, size: 220, color: cs.primary),
          _GlowingCircle(
              bottom: -80, right: -30, size: 260, color: cs.secondary),

          SafeArea(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(20, 12, 20, 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  _AttemptsBar(c: c),
                  const SizedBox(height: 16),
                  Expanded(child: Center(child: _buildMainButton(cs))),
                  const SizedBox(height: 12),
                  _HintText(phase: _phase),
                ],
              ),
            ),
          ),

          if (_phase == _Phase.lockout)
            FadeTransition(
              opacity:
                  _lockoutCtrl.drive(CurveTween(curve: Curves.easeOutCubic)),
              child: Container(
                alignment: Alignment.center,
                color: Colors.black.withOpacity(0.25),
                child: _glass(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 14, horizontal: 18),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.block, color: Colors.redAccent),
                        const SizedBox(width: 8),
                        Text(context.l10n.test_too_soon_hint,
                            style: Theme.of(context).textTheme.titleSmall),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMainButton(ColorScheme cs) {
    final bool isGreen = _phase == _Phase.green;
    final bool isCounting = _phase == _Phase.countdown;

    final String label = switch (_phase) {
      _Phase.countdown => '$_countdown',
      _Phase.waiting => context.l10n.test_wait,
      _Phase.green => context.l10n.test_tap_now,
      _Phase.lockout => context.l10n.test_too_fast,
    };

    final Gradient gradient = isGreen
        ? LinearGradient(colors: [cs.primary, cs.secondary])
        : LinearGradient(
            colors: [cs.surfaceVariant, cs.surfaceVariant.withOpacity(0.8)]);

    final Color textColor = isGreen ? Colors.white : cs.onSurfaceVariant;

    return GestureDetector(
      onTapDown: (_) => isGreen ? _pressCtrl.forward() : null,
      onTapCancel: () => _pressCtrl.reverse(),
      onTapUp: (_) => _pressCtrl.reverse(),
      onTap: _onTap,
      child: AnimatedBuilder(
        animation: _pressScale,
        builder: (context, child) =>
            Transform.scale(scale: _pressScale.value, child: child),
        child: ConstrainedBox(
          constraints: const BoxConstraints.tightFor(width: 260, height: 260),
          child: Stack(
            alignment: Alignment.center,
            children: [
              ClipOval(
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 6, sigmaY: 6),
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: gradient,
                      boxShadow: [
                        BoxShadow(
                          color: (isGreen ? cs.primary : cs.outlineVariant)
                              .withOpacity(0.35),
                          blurRadius: 32,
                          offset: const Offset(0, 12),
                        ),
                      ],
                    ),
                    child: const SizedBox.expand(),
                  ),
                ),
              ),
              AnimatedSwitcher(
                duration: const Duration(milliseconds: 180),
                transitionBuilder: (child, anim) =>
                    FadeTransition(opacity: anim, child: child),
                child: Text(
                  label,
                  key: ValueKey(label),
                  style: Theme.of(context).textTheme.displaySmall?.copyWith(
                        color: textColor,
                        fontWeight: FontWeight.w800,
                      ),
                  textAlign: TextAlign.center,
                ),
              ),
              if (isCounting)
                Positioned(
                  bottom: 18,
                  child: Text(
                      'Round ${c.attempt + 1} / ${CountController.maxAttempts}',
                      style: Theme.of(context)
                          .textTheme
                          .labelLarge
                          ?.copyWith(color: textColor.withOpacity(0.9))),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _glass({required Widget child}) {
    final cs = Theme.of(context).colorScheme;
    return ClipRRect(
      borderRadius: BorderRadius.circular(14),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: DecoratedBox(
          decoration: BoxDecoration(
            color: cs.surface.withOpacity(0.55),
            borderRadius: BorderRadius.circular(14),
            border: Border.all(color: cs.outlineVariant.withOpacity(0.7)),
          ),
          child: child,
        ),
      ),
    );
  }
}

class _AttemptsBar extends StatelessWidget {
  const _AttemptsBar({required this.c});

  final CountController c;

  String _ms(int? v) => v == null ? '-' : '${v}ms';

  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;
    return Obx(() {
      final a = c.attempt; // 0..3
      final f = c.firstRecord.value;
      final s = c.secondRecord.value;
      final t = c.thirdRecord.value;

      Widget chip(String title, String value, bool active) => Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
              margin: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: active ? cs.primary.withOpacity(0.12) : cs.surface,
                borderRadius: BorderRadius.circular(14),
                border: Border.all(
                    color: active
                        ? cs.primary.withOpacity(0.28)
                        : cs.outlineVariant),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(title,
                      style: Theme.of(context)
                          .textTheme
                          .labelMedium
                          ?.copyWith(color: cs.onSurfaceVariant)),
                  const SizedBox(height: 4),
                  Text(value,
                      style: Theme.of(context)
                          .textTheme
                          .titleSmall
                          ?.copyWith(fontWeight: FontWeight.w700)),
                ],
              ),
            ),
          );

      return Row(
        children: [
          chip(context.l10n.test_try1_short, _ms(f), a == 0),
          chip(context.l10n.test_try2_short, _ms(s), a == 1),
          chip(context.l10n.test_try3_short, _ms(t), a == 2),
        ],
      );
    });
  }
}

class _HintText extends StatelessWidget {
  const _HintText({required this.phase});

  final _Phase phase;

  @override
  Widget build(BuildContext context) {
    final text = Theme.of(context).textTheme;
    switch (phase) {
      case _Phase.countdown:
        return Text(context.l10n.test_hint_countdown, style: text.bodySmall);
      case _Phase.waiting:
        return Text(context.l10n.test_hint_waiting, style: text.bodySmall);
      case _Phase.green:
        return Text(context.l10n.test_hint_green, style: text.bodySmall);
      case _Phase.lockout:
        return Text(context.l10n.test_hint_lockout, style: text.bodySmall);
    }
  }
}

class _GlowingCircle extends StatelessWidget {
  const _GlowingCircle(
      {this.top,
      this.left,
      this.right,
      this.bottom,
      required this.size,
      required this.color});

  final double? top;
  final double? left;
  final double? right;
  final double? bottom;
  final double size;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: top,
      left: left,
      right: right,
      bottom: bottom,
      child: IgnorePointer(
        ignoring: true,
        child: Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                color.withOpacity(0.22),
                color.withOpacity(0.06),
                Colors.transparent,
              ],
              stops: const [0.0, 0.45, 1.0],
            ),
          ),
          child:  BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 24, sigmaY: 24),
              child: const SizedBox()),
        ),
      ),
    );
  }
}
