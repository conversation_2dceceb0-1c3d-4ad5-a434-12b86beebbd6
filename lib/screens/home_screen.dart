import 'dart:ui';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_reaction_speed/util/tier_emblem.dart';
import 'package:get/get.dart';
import 'package:flutter_reaction_speed/main.dart';

import 'package:flutter_reaction_speed/services/supabase_service.dart';
import 'package:flutter_reaction_speed/routes/app_pages.dart';

// --------------------------------- Screen ----------------------------------
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late final _HomeController c;

  @override
  void initState() {
    super.initState();
    c = Get.put(_HomeController(), permanent: false);
    c.loadAll();
  }

  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.transparent,
        title: Text(context.l10n.home_title),
        actions: [
          IconButton(
            icon: const Icon(Icons.emoji_events_outlined),
            tooltip: context.l10n.home_tooltip_leaderboard,
            onPressed: () => Get.toNamed(Routes.LEADERBOARD),
          ),
          IconButton(
            icon: const Icon(Icons.person_outline),
            tooltip: context.l10n.home_tooltip_profile,
            onPressed: () => Get.toNamed(Routes.PROFILE),
          ),
          const SizedBox(width: 4),
        ],
      ),
      body: Stack(
        fit: StackFit.expand,
        children: [
          // gradient bg
          DecoratedBox(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  cs.primaryContainer.withOpacity(0.35),
                  cs.surfaceVariant.withOpacity(0.3),
                  cs.secondaryContainer.withOpacity(0.35),
                ],
              ),
            ),
          ),
          const _GlowingCircle(top: -60, left: -40, size: 220),
          const _GlowingCircle(bottom: -80, right: -30, size: 260),

          SafeArea(
            child: RefreshIndicator(
              onRefresh: () async => c.loadAll(),
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.fromLTRB(20, 16, 20, 28),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _Greeting(controller: c),
                    const SizedBox(height: 16),

                    // Tier / My stats card
                    _TierAndStatsCard(controller: c),
                    const SizedBox(height: 18),

                    // Leaderboard preview
                    _LeaderboardPreview(controller: c),

                    const SizedBox(height: 18),

                    // How to play
                    TextButton.icon(
                      icon: const Icon(Icons.help_outline),
                      label: Text(context.l10n.home_how_to_play),
                      style: TextButton.styleFrom(
                          padding: const EdgeInsets.symmetric(
                              vertical: 12, horizontal: 14)),
                      onPressed: () => _showHowToPlayGlassModalCentered(context,
                          onStart: () => Get.toNamed(Routes.REACTION_TEST)),
                    ),
                    const SizedBox(height: 10),

                    // Start button
                    _StartButton(
                      label: context.l10n.home_start,
                      onPressed: () => Get.toNamed(Routes.REACTION_TEST),
                    ),

                    const SizedBox(height: 18),
                    Center(
                      child: Text(
                        context.l10n.home_footer,
                        style: Theme.of(context)
                            .textTheme
                            .bodySmall
                            ?.copyWith(color: cs.onSurfaceVariant),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// ------------------------------- Controller --------------------------------
class _HomeController extends GetxController {
  final service = SupabaseService();

  // profile
  final username = RxnString();
  final countryCode = RxnString();

  // my stats
  final myBestMs = RxnInt();
  final myRank = RxnInt();
  final myPercentile = RxnDouble(); // global percentile (lower = better)
  final tierName = RxnString();
  final tierAsset = RxnString();

  // leaderboard previews
  final topPlayers =
      <Map<String, dynamic>>[].obs; // [{rank, username, country_code, best_ms}]
  final topCountryPlayers = <Map<String, dynamic>>[].obs; // same shape

  final loadingProfile = false.obs;
  final loadingStats = false.obs;
  final loadingGlobal = false.obs;
  final loadingCountry = false.obs;

  Future<void> loadAll() async {
    await Future.wait([_loadProfile(), _loadStats()]);
    await Future.wait([_loadGlobalTop(), _loadCountryTop()]);
  }

  Future<void> _loadProfile() async {
    loadingProfile.value = true;
    try {
      final me = await service.getMyProfile();
      username.value = (me?['username'] as String?)?.trim();
      countryCode.value = (me?['country_code'] as String?)?.toUpperCase();
    } catch (_) {
    } finally {
      loadingProfile.value = false;
    }
  }

  Future<void> _loadStats() async {
    loadingStats.value = true;
    try {
      final s = await service.getMyStats();
      if (s != null) {
        myBestMs.value = (s['my_best_ms'] as num?)?.toInt();
        myRank.value = (s['my_global_rank'] as num?)?.toInt();
        myPercentile.value = (s['global_percentile'] as num?)?.toDouble();
        tierName.value = s['tier'] as String?;
        tierAsset.value = service.tierAssetFrom(s) ??
            s['tier_asset'] as String?; // optional server-provided
      }
    } catch (_) {
    } finally {
      loadingStats.value = false;
    }
  }

  Future<void> _loadGlobalTop() async {
    loadingGlobal.value = true;
    try {
      final rows = await service.getLeaderboard(limit: 5, offset: 0);
      topPlayers.assignAll(rows);
    } catch (_) {
    } finally {
      loadingGlobal.value = false;
    }
  }

  Future<void> _loadCountryTop() async {
    loadingCountry.value = true;
    try {
      final cc = countryCode.value;
      if (cc == null || cc.isEmpty) {
        topCountryPlayers.clear();
      } else {
        final rows =
            await service.getLeaderboard(country: cc, limit: 5, offset: 0);
        topCountryPlayers.assignAll(rows);
      }
    } catch (_) {
    } finally {
      loadingCountry.value = false;
    }
  }
}

// -------------------------------- Widgets ----------------------------------
class _Greeting extends StatelessWidget {
  const _Greeting({required this.controller});

  final _HomeController controller;

  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;
    return Row(
      children: [
        Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                  color: cs.primary.withOpacity(0.25),
                  blurRadius: 24,
                  spreadRadius: 4)
            ],
            gradient: LinearGradient(
                colors: [cs.primary, cs.secondary],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight),
          ),
          child: const Center(child: Icon(Icons.bolt, color: Colors.white)),
        ),
        const SizedBox(width: 14),
        Expanded(
          child: Obx(() {
            final name = controller.username.value;
            final greet =
                (name != null && name.isNotEmpty) ? '안녕하세요, $name!' : '안녕하세요!';
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  greet,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context)
                      .textTheme
                      .titleMedium
                      ?.copyWith(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 4),
                Text(
                  '전 세계 유저와 반응속도를 겨뤄보세요',
                  style: Theme.of(context).textTheme.bodySmall,
                  overflow: TextOverflow.ellipsis,
                )
              ],
            );
          }),
        ),
      ],
    );
  }
}

class _TierAndStatsCard extends StatelessWidget {
  const _TierAndStatsCard({required this.controller});

  final _HomeController controller;

  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(color: cs.outlineVariant),
      ),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(14, 12, 14, 12),
        child: Row(
          children: [
            // Tier emblem
            SizedBox(
              width: 72,
              height: 72,
              child: Obx(() {
                final asset = controller.tierAsset.value;
                if (asset != null && asset.isNotEmpty) {
                  return TierEmblem(
                    asset: controller.tierAsset.value,
                    tierKey: controller.tierName.value, // 'Gold' / 'Challenger' 등 (없어도 asset 이름에서 추론)
                    // tierKey: 'bronze',
                    size: 72, // 72~96 사이 추천
                  );
                }
                // skeleton circle
                return ClipOval(
                  child: Container(
                    color: cs.surfaceVariant,
                    child: const SizedBox.expand(),
                  ),
                );
              }),
            ),
            const SizedBox(width: 14),
            // Stats
            Expanded(
              child: Obx(() {
                final best = controller.myBestMs.value;
                final rank = controller.myRank.value;
                final pct = controller.myPercentile.value;
                final tier = controller.tierName.value;

                final loading = controller.loadingStats.value;
                if (loading) {
                  return const _StatsSkeleton();
                }

                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(children: [
                      _Kpi(
                          label: '나의 베스트',
                          value: best != null ? '${best}ms' : '-'),
                      const SizedBox(width: 12),
                      _Kpi(
                          label: '글로벌 순위',
                          value: rank != null ? '#$rank' : '-'),
                    ]),
                    const SizedBox(height: 8),
                    Row(children: [
                      _Kpi(
                          label: '상위',
                          value:
                              pct != null ? '${pct.toStringAsFixed(2)}%' : '-'),
                      const SizedBox(width: 12),
                      Flexible(
                          child: Text(
                        tier ?? '-',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: Theme.of(context).textTheme.labelLarge,
                      )),
                    ]),
                  ],
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}

class _Kpi extends StatelessWidget {
  const _Kpi({required this.label, required this.value});

  final String label;
  final String value;

  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label,
            style: Theme.of(context)
                .textTheme
                .bodySmall
                ?.copyWith(color: cs.onSurfaceVariant)),
        const SizedBox(height: 2),
        Text(value,
            style: Theme.of(context)
                .textTheme
                .titleSmall
                ?.copyWith(fontWeight: FontWeight.w700)),
      ],
    );
  }
}

class _StatsSkeleton extends StatelessWidget {
  const _StatsSkeleton();

  @override
  Widget build(BuildContext context) {
    final base = Theme.of(context).colorScheme.surfaceVariant;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(children: [
          _SkeletonLine(width: 80, height: 14, color: base),
          const SizedBox(width: 12),
          _SkeletonLine(width: 80, height: 14, color: base),
        ]),
        const SizedBox(height: 10),
        Row(children: [
          _SkeletonLine(width: 60, height: 14, color: base),
          const SizedBox(width: 12),
          _SkeletonLine(width: 100, height: 14, color: base),
        ]),
      ],
    );
  }
}

class _SkeletonLine extends StatefulWidget {
  const _SkeletonLine(
      {required this.width, required this.height, required this.color});

  final double width;
  final double height;
  final Color color;

  @override
  State<_SkeletonLine> createState() => _SkeletonLineState();
}

class _SkeletonLineState extends State<_SkeletonLine>
    with SingleTickerProviderStateMixin {
  late final AnimationController _ac;
  late final Animation<double> _a;

  @override
  void initState() {
    super.initState();
    _ac = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 1200))
      ..repeat();
    _a = CurvedAnimation(parent: _ac, curve: Curves.easeInOut);
  }

  @override
  void dispose() {
    _ac.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _a,
      builder: (_, __) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            color: Color.lerp(
                widget.color.withOpacity(0.6),
                widget.color.withOpacity(0.25),
                (sin(_a.value * pi * 2) + 1) / 2),
            borderRadius: BorderRadius.circular(6),
          ),
        );
      },
    );
  }
}

class _LeaderboardPreview extends StatefulWidget {
  const _LeaderboardPreview({required this.controller});

  final _HomeController controller;

  @override
  State<_LeaderboardPreview> createState() => _LeaderboardPreviewState();
}

class _LeaderboardPreviewState extends State<_LeaderboardPreview> {
  final _tab = 0.obs; // 0: 개인, 1: 국가
  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;
    final c = widget.controller;

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: BorderSide(color: cs.outlineVariant)),
      child: Padding(
        padding: const EdgeInsets.fromLTRB(16, 14, 16, 12),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(children: [
            Expanded(
              child: Obx(() => SegmentedButton<int>(
                    segments: [
                      ButtonSegment(value: 0, label: Text(context.l10n.home_tab_personal)),
                      ButtonSegment(value: 1, label: Text(context.l10n.home_tab_country)),
                    ],
                    selected: {_tab.value},
                    onSelectionChanged: (s) => _tab.value = s.first,
                  )),
            ),
            TextButton(
              onPressed: () => Get.toNamed(Routes.LEADERBOARD, parameters: {
                'tab': _tab.value == 0 ? 'personal' : 'country'
              }),
              child: Text(context.l10n.home_view_all),
            )
          ]),
          const SizedBox(height: 8),
          Obx(() {
            final isPersonal = _tab.value == 0;
            final items = isPersonal ? c.topPlayers : c.topCountryPlayers;
            final loading =
                isPersonal ? c.loadingGlobal.value : c.loadingCountry.value;

            if (loading) return const _LBPreviewSkeleton();
            if (items.isEmpty) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 12.0),
                child: Text(context.l10n.home_empty_preview,
                    style: Theme.of(context).textTheme.bodyMedium),
              );
            }

            return Column(
              children: [
                for (var i = 0; i < min(3, items.length); i++)
                  _LBRow(item: items[i], rank: i + 1),
              ],
            );
          })
        ]),
      ),
    );
  }
}

class _LBRow extends StatelessWidget {
  const _LBRow({required this.item, required this.rank});

  final Map<String, dynamic> item;
  final int rank;

  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;
    final username = (item['username'] as String?)?.trim();
    final displayName =
        (username == null || username.isEmpty) ? '익명' : username;
    final bestMs = (item['best_ms'] as num?)?.toInt();
    final cc = (item['country_code'] as String?)?.toUpperCase();

    IconData trophy(int r) => switch (r) {
          1 => Icons.emoji_events,
          2 => Icons.emoji_events_outlined,
          _ => Icons.military_tech_outlined
        };

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(children: [
        SizedBox(
            width: 30,
            child: Center(
                child: Text('#$rank',
                    style: Theme.of(context)
                        .textTheme
                        .titleSmall
                        ?.copyWith(fontWeight: FontWeight.w700)))),
        Icon(trophy(rank), color: cs.primary),
        const SizedBox(width: 10),
        Expanded(
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(displayName,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: Theme.of(context)
                    .textTheme
                    .bodyLarge
                    ?.copyWith(fontWeight: FontWeight.w600)),
            const SizedBox(height: 2),
            Text('${_flag(cc)} ${cc ?? '—'}',
                style: Theme.of(context)
                    .textTheme
                    .bodySmall
                    ?.copyWith(color: cs.onSurfaceVariant),
                maxLines: 1,
                overflow: TextOverflow.ellipsis),
          ]),
        ),
        const SizedBox(width: 8),
        Text(bestMs != null ? '${bestMs}ms' : '-',
            style: Theme.of(context).textTheme.titleSmall),
      ]),
    );
  }

  String _flag(String? code) {
    if (code == null || code.length != 2) return '🏳️';
    final base = 0x1F1E6;
    final a = code.codeUnitAt(0) - 0x41;
    final b = code.codeUnitAt(1) - 0x41;
    return String.fromCharCode(base + a) + String.fromCharCode(base + b);
  }
}

class _LBPreviewSkeleton extends StatelessWidget {
  const _LBPreviewSkeleton();

  @override
  Widget build(BuildContext context) {
    final base = Theme.of(context).colorScheme.surfaceVariant;
    return Column(children: const [
      _LBRowSkel(),
      _LBRowSkel(),
      _LBRowSkel(),
    ]);
  }
}

class _LBRowSkel extends StatelessWidget {
  const _LBRowSkel();

  @override
  Widget build(BuildContext context) {
    final base = Theme.of(context).colorScheme.surfaceVariant;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(children: [
        const SizedBox(width: 30),
        _SkeletonCircle(size: 20, color: base),
        const SizedBox(width: 10),
        const Expanded(
            child: _SkeletonLine(
                width: double.infinity, height: 16, color: Colors.transparent)),
        _SkeletonLine(width: 60, height: 14, color: base),
      ]),
    );
  }
}

class _SkeletonCircle extends StatefulWidget {
  const _SkeletonCircle({required this.size, required this.color});

  final double size;
  final Color color;

  @override
  State<_SkeletonCircle> createState() => _SkeletonCircleState();
}

class _SkeletonCircleState extends State<_SkeletonCircle>
    with SingleTickerProviderStateMixin {
  late final AnimationController _ac;
  late final Animation<double> _a;

  @override
  void initState() {
    super.initState();
    _ac = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 1200))
      ..repeat();
    _a = CurvedAnimation(parent: _ac, curve: Curves.easeInOut);
  }

  @override
  void dispose() {
    _ac.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _a,
      builder: (_, __) => Container(
        width: widget.size,
        height: widget.size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Color.lerp(widget.color.withOpacity(0.6),
              widget.color.withOpacity(0.25), (sin(_a.value * pi * 2) + 1) / 2),
        ),
      ),
    );
  }
}

// ------------------------------ Reusable UI -------------------------------
class _StartButton extends StatefulWidget {
  const _StartButton({required this.label, required this.onPressed});

  final String label;
  final VoidCallback onPressed;

  @override
  State<_StartButton> createState() => _StartButtonState();
}

class _StartButtonState extends State<_StartButton>
    with SingleTickerProviderStateMixin {
  late final AnimationController _ac;
  late final Animation<double> _scale;

  @override
  void initState() {
    super.initState();
    _ac = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 220),
        lowerBound: 0,
        upperBound: 0.06);
    _scale = Tween<double>(begin: 1.0, end: 0.94)
        .animate(CurvedAnimation(parent: _ac, curve: Curves.easeOutCubic));
  }

  @override
  void dispose() {
    _ac.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;
    return GestureDetector(
      onTapDown: (_) => _ac.forward(),
      onTapCancel: () => _ac.reverse(),
      onTapUp: (_) => _ac.reverse(),
      onTap: widget.onPressed,
      child: AnimatedBuilder(
        animation: _scale,
        builder: (_, child) =>
            Transform.scale(scale: _scale.value, child: child),
        child: ConstrainedBox(
          constraints: const BoxConstraints(minHeight: 56),
          child: DecoratedBox(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                  colors: [cs.primary, cs.secondary],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight),
              borderRadius: BorderRadius.circular(18),
              boxShadow: [
                BoxShadow(
                    color: cs.primary.withOpacity(0.35),
                    blurRadius: 28,
                    offset: const Offset(0, 10))
              ],
            ),
            child: Center(
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 16.0, horizontal: 12),
                child: Row(mainAxisSize: MainAxisSize.min, children: [
                  const Icon(Icons.play_arrow_rounded, color: Colors.white),
                  const SizedBox(width: 6),
                  Text(widget.label,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.white, fontWeight: FontWeight.w700)),
                ]),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class _GlowingCircle extends StatelessWidget {
  const _GlowingCircle(
      {this.top, this.left, this.right, this.bottom, required this.size});

  final double? top;
  final double? left;
  final double? right;
  final double? bottom;
  final double size;

  @override
  Widget build(BuildContext context) {
    final cs = Theme.of(context).colorScheme;
    return Positioned(
      top: top,
      left: left,
      right: right,
      bottom: bottom,
      child: IgnorePointer(
        ignoring: true,
        child: Container(
          width: size,
          height: size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                cs.primary.withOpacity(0.22),
                cs.primary.withOpacity(0.06),
                Colors.transparent
              ],
              stops: const [0.0, 0.45, 1.0],
            ),
          ),
          child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 24, sigmaY: 24),
              child: const SizedBox()),
        ),
      ),
    );
  }
}

// ------------------------------- How to play -------------------------------
void _showHowToPlayGlassModalCentered(BuildContext context,
    {VoidCallback? onStart}) {
  final cs = Theme.of(context).colorScheme;
  showDialog(
    context: context,
    barrierDismissible: true,
    builder: (_) => Center(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Material(
            color: cs.surface.withOpacity(0.6),
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 420),
              child: Padding(
                padding: const EdgeInsets.fromLTRB(20, 18, 20, 12),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(children: [
                      const Icon(Icons.flash_on),
                      const SizedBox(width: 8),
                      Text(context.l10n.how_title,
                          style: Theme.of(context)
                              .textTheme
                              .titleLarge
                              ?.copyWith(fontWeight: FontWeight.w800)),
                    ]),
                    const SizedBox(height: 10),
                    const Text(
                        '3초 카운트다운 이후 버튼 색이 초록으로 바뀌면 즉시 탭하세요. 총 3번의 시도를 진행합니다.'),
                    const SizedBox(height: 6),
                    const Text('글로벌 리더보드는 개인 최고 기록 기준으로 집계됩니다.'),
                    const SizedBox(height: 16),
                    Align(
                      alignment: Alignment.centerRight,
                      child: FilledButton.icon(
                        icon: const Icon(Icons.play_arrow_rounded),
                        label: Text(context.l10n.how_start_now),
                        onPressed: () {
                          Navigator.of(context).pop();
                          onStart?.call();
                        },
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    ),
  );
}
